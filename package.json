{"name": "esim-store", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:mobile": "vite --config vite.mobile.config.ts", "build": "run-p type-check \"build-only {@}\" --", "build:mobile": "vite build mobile/index.html --outDir dist/mobile", "preview": "vite preview", "preview:mobile": "vite preview dist/mobile", "build-only": "vite build", "build:all": "npm run build && npm run build:mobile", "type-check": "vue-tsc --build", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.1.0", "await-to-js": "^3.0.0", "axios": "^1.9.0", "dayjs": "^1.11.13", "element-plus": "^2.9.8", "fast-glob": "^3.3.3", "lib-flexible": "^0.3.2", "pinia": "^3.0.1", "swiper": "^11.2.8", "vant": "^4.9.19", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.5.13", "vue-hooks-plus": "2.2.3", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@types/postcss-pxtorem": "^6.1.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "postcss-pxtorem": "^6.1.0", "prettier": "3.5.3", "sass-embedded": "^1.87.0", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}