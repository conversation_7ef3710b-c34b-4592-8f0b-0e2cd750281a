import type { AxiosRequestConfig, AxiosResponse, Method } from 'axios'
import axios, { AxiosError } from 'axios'
// todo 适配web与h5
import { ElMessage } from 'element-plus'
import to from 'await-to-js'

const fetch = axios.create({
  adapter: 'fetch',
  timeout: 10000,
  // baseURL: import.meta.env.VITE_API_URL,
})

function getErrorMsg() {
  return {
    CLIENT_ERROR: '客户端出错，请稍后再试!',
    SERVER_ERROR: '服务器出错，请稍后再试!',
    DEFAULT_ERROR: '未知错误，请稍后再试!',
  }
}

enum ResponseCode {
  REDIRECTION = '302',
  UNAUTHORIZED = '401',
}

function handleError(msg: string = getErrorMsg().DEFAULT_ERROR) {
  ElMessage.error(msg)
}
fetch.interceptors.request.use(
  (config) => {
    if (config.headers) {
      // config.headers['x-referer'] = location.href
      // if (getToken()) {
      //   config.headers.Authorization = getToken()
      // }
    }
    return config
  },
  (error) => {
    handleError(error.message || getErrorMsg().CLIENT_ERROR)
    return Promise.reject(error)
  },
)
fetch.interceptors.response.use(
  (response: AxiosResponse<any>) => {
    const { data, config } = response
    if (config.fetchOptions?.rawData) {
      return response
    }
    if (
      'responseCode' in data &&
      (ResponseCode.REDIRECTION === data.responseCode ||
        ResponseCode.UNAUTHORIZED === data.responseCode)
    ) {
      // location.replace(data.data.redirectPath)
      return response
    }
    if ('resultCode' in data) {
      if (data.resultCode !== '00000000') {
        handleError(data.resultDesc || getErrorMsg().SERVER_ERROR)
        return Promise.reject(response)
      }
      return response
    }
    return response
  },
  (error) => {
    if (error.code === AxiosError.ERR_CANCELED || error.code === AxiosError.ECONNABORTED) {
      return
    }
    handleError(error.message || getErrorMsg().SERVER_ERROR)
    return Promise.reject(error)
  },
)

async function formatRequest<T>(
  method: Method,
  config: AxiosRequestConfig,
  headers: AxiosRequestConfig['headers'] = {},
) {
  const formatConfig = {
    method,
    ...config,
    headers: {
      ...headers,
      ...config.headers,
    },
  }
  const [error, response] = await to<AxiosResponse<T>>(fetch.request(formatConfig))

  if (!error && response) {
    return response.data
  }
}

// GET 请求方法
export function get<T>(config: AxiosRequestConfig) {
  return formatRequest<T>('GET', config)
}

// POST JSON 请求方法
export function postJSON<T>(config: AxiosRequestConfig) {
  return formatRequest<T>('POST', config, {
    ...config.headers,
    'Content-Type': 'application/json',
  })
}

export function postFormData<T>(config: AxiosRequestConfig) {
  return formatRequest<T>('POST', config, {
    ...config.headers,
    'Content-Type': 'multipart/form-data',
  })
}

export { fetch }
