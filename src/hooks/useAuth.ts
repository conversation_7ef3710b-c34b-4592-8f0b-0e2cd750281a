import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

// 模拟已注册的邮箱列表
const EXISTING_EMAILS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']

export const useAuth = () => {
  // 表单状态
  const email = ref('')
  const password = ref('')
  const retypePassword = ref('')
  const verificationCode = ref('')
  const selectedCountry = ref('')
  const isLoading = ref(false)
  const showPassword = ref(false)
  const showRetypePassword = ref(false)
  const codeSent = ref(false)
  const countdown = ref(0)
  const currentStep = ref<'email' | 'register' | 'country'>('email')

  // 验证状态
  const emailValid = computed(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email.value)
  })

  const passwordValid = computed(() => {
    return (
      password.value.length >= 6 &&
      password.value.length <= 20 &&
      /[a-zA-Z]/.test(password.value) &&
      /[0-9]/.test(password.value) &&
      /[!@#$%^&*(),.?":{}|<>]/.test(password.value)
    )
  })

  const passwordsMatch = computed(() => {
    return password.value === retypePassword.value && password.value.length > 0
  })

  const codeValid = computed(() => {
    return verificationCode.value.length === 6 && /^\d{6}$/.test(verificationCode.value)
  })

  const passwordError = computed(() => {
    if (retypePassword.value && !passwordsMatch.value) {
      return 'The passwords you entered do not match. Please try again.'
    }
    return ''
  })

  // 注册表单是否可以继续到地区选择
  const canContinueToCountry = computed(() => {
    return emailValid.value && passwordValid.value && passwordsMatch.value
  })

  // 最终注册是否可以提交
  const canSubmitRegister = computed(() => {
    return canContinueToCountry.value && selectedCountry.value
  })

  // 检查用户是否存在
  const checkUserExists = async (emailAddress: string): Promise<boolean> => {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 800))
    return EXISTING_EMAILS.includes(emailAddress.toLowerCase())
  }

  // 登录
  const login = async (emailAddress: string, userPassword: string) => {
    isLoading.value = true
    try {
      // 模拟登录API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // 检查邮箱是否存在
      const userExists = await checkUserExists(emailAddress)
      if (!userExists) {
        throw new Error('User not found')
      }

      return { success: true, message: 'Login successful!' }
    } catch (error) {
      return { success: false, message: 'Login failed. Please check your credentials.' }
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (emailAddress: string, userPassword: string, countryCode?: string) => {
    isLoading.value = true
    try {
      // 检查邮箱是否已存在
      const userExists = await checkUserExists(emailAddress)
      if (userExists) {
        return {
          success: false,
          message: 'This email is already registered. Please try logging in instead.',
          shouldRedirectToLogin: true,
        }
      }

      // 模拟注册API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))

      return {
        success: true,
        message: 'Registration successful!',
        data: {
          email: emailAddress,
          country: countryCode,
        },
      }
    } catch (error) {
      return { success: false, message: 'Registration failed. Please try again.' }
    } finally {
      isLoading.value = false
    }
  }

  // 发送验证码
  const sendVerificationCode = async (emailAddress: string) => {
    isLoading.value = true
    try {
      // 模拟发送验证码API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      codeSent.value = true
      countdown.value = 60

      // 开始倒计时
      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)

      return { success: true, message: 'Verification code sent to your email!' }
    } catch (error) {
      return { success: false, message: 'Failed to send verification code. Please try again.' }
    } finally {
      isLoading.value = false
    }
  }

  // 重置密码
  const resetPassword = async (emailAddress: string, code: string, newPassword: string) => {
    isLoading.value = true
    try {
      // 模拟重置密码API
      await new Promise((resolve) => setTimeout(resolve, 1500))

      return { success: true, message: 'Password reset successful!' }
    } catch (error) {
      return { success: false, message: 'Failed to reset password. Please try again.' }
    } finally {
      isLoading.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    email.value = ''
    password.value = ''
    retypePassword.value = ''
    verificationCode.value = ''
    selectedCountry.value = ''
    showPassword.value = false
    showRetypePassword.value = false
    codeSent.value = false
    countdown.value = 0
    currentStep.value = 'email'
  }

  // 继续到地区选择步骤
  const continueToCountrySelection = () => {
    if (canContinueToCountry.value) {
      currentStep.value = 'country'
      return true
    }
    return false
  }

  // 返回到注册表单
  const backToRegisterForm = () => {
    currentStep.value = 'register'
  }

  // 设置选中的国家
  const setSelectedCountry = (countryCode: string) => {
    selectedCountry.value = countryCode
  }

  // 社交登录
  const socialLogin = async (provider: 'google' | 'apple' | 'mobile') => {
    isLoading.value = true
    try {
      // 模拟社交登录API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      return { success: true, message: `${provider} login successful!` }
    } catch (error) {
      return { success: false, message: `${provider} login failed. Please try again.` }
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 状态
    email,
    password,
    retypePassword,
    verificationCode,
    selectedCountry,
    isLoading,
    showPassword,
    showRetypePassword,
    codeSent,
    countdown,
    currentStep,

    // 计算属性
    emailValid,
    passwordValid,
    passwordsMatch,
    codeValid,
    passwordError,
    canContinueToCountry,
    canSubmitRegister,

    // 方法
    checkUserExists,
    login,
    register,
    sendVerificationCode,
    resetPassword,
    resetForm,
    socialLogin,
    continueToCountrySelection,
    backToRegisterForm,
    setSelectedCountry,
  }
}
