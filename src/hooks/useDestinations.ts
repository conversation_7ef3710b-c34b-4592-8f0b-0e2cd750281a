import { ref, computed, readonly } from 'vue'
import { useRouter } from 'vue-router'
import { useRequest } from 'vue-hooks-plus'
import { getPopularDestinations, DestinationKeyword } from '@/api'
import type { PopularDestination } from '@/api/Country/types'
import { DestinationType } from '@/data/destinations'
import { paramsWrap } from '@/api/utils'

/**
 * 热门目的地 Hook
 * 提供热门目的地数据获取和过滤功能
 */
export function useDestinations() {
  // 响应式数据
  const destinations = ref<PopularDestination[]>([])
  const activeTab = ref(DestinationType.LOCAL)

  // 获取热门目的地数据
  const { loading, error, refresh } = useRequest(
    () => getPopularDestinations(paramsWrap({ type: DestinationKeyword.HOT_ESIM_KEYWORD })),
    {
      staleTime: 50000,
      onSuccess: (data) => {
        destinations.value = data?.data || []
      },
      onError: (err) => {
        console.error('获取热门目的地失败:', err)
      },
    },
  )

  // 根据当前选中的标签过滤目的地列表
  const filteredDestinations = computed(() => {
    let temp = destinations.value
    if (activeTab.value === DestinationType.REGION) {
      temp = temp.filter((d) => (d.keyType || '').toLowerCase() === DestinationType.REGION)
    }
    return temp
  })

  // 限制显示数量的目的地列表（默认16个）
  const limitedDestinations = computed(() => {
    return filteredDestinations.value.slice(0, 16)
  })

  // 获取指定数量的目的地列表
  const getDestinationsByLimit = (limit: number) => {
    return filteredDestinations.value.slice(0, limit)
  }

  // 切换标签
  const setActiveTab = (tab: DestinationType) => {
    activeTab.value = tab
  }

  // 重置数据
  const reset = () => {
    destinations.value = []
    activeTab.value = DestinationType.LOCAL
  }

  return {
    // 响应式数据
    destinations: readonly(destinations),
    activeTab: readonly(activeTab),
    loading,
    error,

    // 计算属性
    filteredDestinations,
    limitedDestinations,

    // 方法
    setActiveTab,
    getDestinationsByLimit,
    refresh,
    reset,
  }
}

/**
 * 目的地导航 Hook
 * 提供目的地相关的路由导航功能
 */
export function useDestinationNavigation() {
  const { push } = useRouter()

  // 跳转到套餐详情页
  const goToPlanDetail = (item: PopularDestination) => {
    push({
      name: 'plan-detail',
      params: {
        pid: item.iso2,
      },
      query: {
        name: item.value,
      },
    })
  }

  // 跳转到套餐列表页
  const goToShopPlans = () => {
    push({ name: 'shop-plans' })
  }

  return {
    goToPlanDetail,
    goToShopPlans,
  }
}
