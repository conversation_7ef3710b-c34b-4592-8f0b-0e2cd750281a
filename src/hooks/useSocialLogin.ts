export const useGoogleLogin = () => {
  const state = reactive({
    loading: false,
    error: null,
    user: null,
    isLoggedIn: false,
    success: null,
  })
  function handleGoogleLogin() {
    state.loading = true
    state.error = null

    // 初始化Google登录
    google.accounts.id.initialize({
      client_id: 'YOUR_GOOGLE_CLIENT_ID.apps.googleusercontent.com',
      callback: handleCredentialResponse,
    })
    // 也可以直接提示登录
    // google.accounts.id.prompt();
  }

  function handleCredentialResponse(response) {
    // 解析JWT token获取用户信息
    const credential = response.credential
    const payload = JSON.parse(atob(credential.split('.')[1]))

    state.user = {
      name: payload.name,
      email: payload.email,
      picture: payload.picture,
      sub: payload.sub,
      email_verified: payload.email_verified,
      createdAt: new Date(payload.iat * 1000).toLocaleString(),
    }

    state.isLoggedIn = true
    state.loading = false
    state.success = '登录成功！'
  }
}
