// 用户相关API实现
import { postJSON } from '@/utils/fetch'
import type {
  QueryUserInfoRequest,
  QueryUserInfoResponse,
  UserLogoutRequest,
  UpdatePasswordRequest,
  QueryBalanceInfoRequest,
  QueryBalanceInfoResponse,
  BalanceTransactionDetailRequest,
  BalanceTransactionDetailResponse,
} from './types'
import type { BaseResponse } from '@/api/types.ts'

// API基础路径
const API_BASE_PATH = '/bss/app/user'

// ==================== 用户查询 ====================

/**
 * 查询用户信息
 * @param params 查询参数
 * @returns 用户信息
 */
export const queryUserInfo = async (
  params: QueryUserInfoRequest,
): Promise<BaseResponse<QueryUserInfoResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryUserInfoResponse>>({
    url: `${API_BASE_PATH}/QueryUserInfo`,
    data: params,
  })
}

// ==================== 用户退出 ====================

/**
 * 用户退出登录
 * @param params 退出参数
 * @returns 退出结果
 */
export const userLogout = async (
  params: UserLogoutRequest,
): Promise<BaseResponse<null> | undefined> => {
  return await postJSON<BaseResponse<null>>({
    url: `${API_BASE_PATH}/UserLogout`,
    data: params,
  })
}

// ==================== 修改密码 ====================

/**
 * 修改用户密码
 * @param params 修改密码参数
 * @returns 修改结果
 */
export const updatePassword = async (
  params: UpdatePasswordRequest,
): Promise<BaseResponse<string> | undefined> => {
  return await postJSON<BaseResponse<string>>({
    url: `${API_BASE_PATH}/UpdatePassword`,
    data: params,
  })
}

// ==================== 余额查询 ====================

/**
 * 查询用户余额信息
 * @param params 查询参数
 * @returns 余额信息
 */
export const queryBalanceInfo = async (
  params: QueryBalanceInfoRequest,
): Promise<BaseResponse<QueryBalanceInfoResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryBalanceInfoResponse>>({
    url: `${API_BASE_PATH}/QueryBalanceInfo`,
    data: params,
  })
}

// ==================== 余额流水查询 ====================

/**
 * 查询余额流水明细
 * @param params 查询参数
 * @returns 余额流水列表
 */
export const balanceTransactionDetail = async (
  params: BalanceTransactionDetailRequest,
): Promise<BaseResponse<BalanceTransactionDetailResponse> | undefined> => {
  return await postJSON<BaseResponse<BalanceTransactionDetailResponse>>({
    url: `${API_BASE_PATH}/BalanceTransactionDetail`,
    data: params,
  })
}

// ==================== 导出所有API ====================

export const userApi = {
  queryUserInfo,
  userLogout,
  updatePassword,
  queryBalanceInfo,
  balanceTransactionDetail,
}

// 默认导出
export default userApi
