// 用户相关API类型定义
import {
  LangType,
  Gender,
  IdentificationType,
  CurrencyType,
  OperateType,
  MainType,
  PayMethod,
} from '@/api'
import type { BaseRequest, BaseResponse } from '../types'
// ==================== 用户查询 ====================

export interface QueryUserInfoRequest extends BaseRequest {
  /** 合作伙伴编码 */
  partnerCode: string
  /** 登录客户ID */
  loginCustomerId: string
  /** 不传默认为false,false不会查询流量包优先标志字段 */
  needPkgFirstFlag?: boolean
  /** 是否查询余额 - 0: 不查，1: 查询 默认不查 */
  queryAmount?: number
}

export interface QueryUserInfoResponse {
  /** 用户编码 */
  userCode: string
  /** 手机号码 */
  phone?: string
  /** 邮件地址 */
  email?: string
  /** 昵称 */
  nickname?: string
  /** 姓 */
  firstname?: string
  /** 名 */
  lastname?: string
  /** 国家码 */
  countryCode?: string
  /** 证件号码 */
  identification?: string
  /** 证件类型 */
  identificationType?: IdentificationType
  /** 性别 */
  gender?: Gender
  /** 国家名 */
  countryName?: string
  /** 省 */
  province?: string
  /** 城市 */
  city?: string
  /** 区/县 */
  area?: string
  /** 街道 */
  street?: string
  /** 详细的小地址 */
  address?: string
  /** 邮编 */
  zipCode?: string
  /** 公司 */
  company?: string
  /** 是否开启基础资费 */
  basePayFlag: boolean
  /** 机构编码 */
  orgCode: string
  /** 机构id */
  orgId: string
  /** 机构名字 */
  orgName: string
  /** mvno编码 */
  mvnoCode: string
  /** mvnoid */
  mvnoId: string
  /** mvno名字 */
  mvnoName: string
  /** 是否配置过站点 */
  enterpriseRemark: string
  /** 按天收费协议开关 */
  groupDiscFlag: string
  /** 流量包优先标志字段 */
  pkgFirstFlag?: boolean
  /** 是否绑定手机号(是否实名) */
  isBindPhone?: boolean
  /** 余额 */
  amount: number
  /** 货币类型 */
  currencyType: CurrencyType
}

// ==================== 用户退出 ====================

export interface UserLogoutRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** Token信息 */
  accessToken: string
}

// 用户退出响应data为null

// ==================== 修改密码 ====================

export interface UpdatePasswordRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 旧密码 - MD5加密一次传输 */
  oldPwd: string
  /** 新密码 - MD5加密一次传输 */
  newPwd: string
}

// ==================== 余额查询 ====================

export interface QueryBalanceInfoRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** Token信息 */
  accessToken: string
}

export interface QueryBalanceInfoResponse {
  /** 帐本名字 */
  balanceName: string
  /** 帐本类型 - 0：表示零钱帐本 */
  balanceType: string
  /** 帐户ID */
  accountId: string
  /** 帐户余额 */
  balance: number
  /** 赠送余额 */
  secondBalance: number
  /** 补偿余额 */
  thirdBalance: number
  /** 货币类型 */
  currencyType: CurrencyType
  /** 状态 - enable: 有效, disable: 无效 */
  status?: string
  /** 创建时间 - GMT0时间戳 */
  createTime?: number
  /** 有效时间 - GMT0时间戳 */
  effectTime?: number
  /** 失效时间 - GMT0时间戳 */
  expireTime?: number
  /** 帐户预占余额(当前用户的可用余额为 balance-preGrantAmout) */
  preGrantAmout: number
}

// ==================== 余额流水查询 ====================

export interface BalanceTransactionDetailRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 语言类型 */
  langType: LangType
  /** 余额流水的类型。默认查询全部 */
  operateTypeList?: OperateType[]
  /** 开始时间，GMT0时间戳 */
  bgTime?: number
  /** 结束时间，GMT0时间戳 */
  endTime?: number
  /** 当前页，默认值为1 */
  currentPage: number
  /** 当前页，默认值为100 */
  perPageCount: number
}

export interface BalanceTransactionItem {
  /** 操作类型 */
  operateType: OperateType
  /** 变动金额 */
  amount: number
  /** 处理类型 */
  mainType: MainType
  /** 客户ID */
  customerId: string
  /** 支付方式 */
  payMethod: PayMethod
  /** 创建时间，GMT0时间戳 */
  createTime: number
  /** 余额计费国家 */
  visitCountry?: string
  /** 购买的套餐名字 */
  goodsNames?: string[]
}

export interface BalanceTransactionDetailResponse {
  /** 每页条数 */
  perPageCount: number
  /** 当前页 */
  currentPage: number
  /** 总条数 */
  totalCount: number
  /** 总页数 */
  totalPageCount: number
  /** 返回的数据列表 */
  dataList: BalanceTransactionItem[]
}

// ==================== API函数类型定义 ====================

export type QueryUserInfoAPI = (
  params: QueryUserInfoRequest,
) => Promise<BaseResponse<QueryUserInfoResponse>>
export type UserLogoutAPI = (params: UserLogoutRequest) => Promise<BaseResponse<null>>
export type UpdatePasswordAPI = (params: UpdatePasswordRequest) => Promise<BaseResponse<string>>
export type QueryBalanceInfoAPI = (
  params: QueryBalanceInfoRequest,
) => Promise<BaseResponse<QueryBalanceInfoResponse>>
export type BalanceTransactionDetailAPI = (
  params: BalanceTransactionDetailRequest,
) => Promise<BaseResponse<BalanceTransactionDetailResponse>>
