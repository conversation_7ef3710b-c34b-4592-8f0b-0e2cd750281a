import dayjs from 'dayjs'
import { DefineCode } from './enums'

let reuqestNo = 0
export const getStreamNo = function () {
  reuqestNo += 1
  const timeNo = dayjs().format('YYYYMMDDHHmmss')
  const serialNo = reuqestNo.toString().padStart(6, '0')
  return DefineCode.STREAM + timeNo + serialNo
}

export const paramsWrap = function (data) {
  return Object.assign(
    {
      streamNo: getStreamNo(),
      partnerCode: DefineCode.PARTNER,
      langType: navigator.language,
    },
    data,
  )
}
