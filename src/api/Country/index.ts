// 国家地区相关API实现
import { postJSON } from '@/utils/fetch'
import type {
  QueryCountryListRequest,
  QueryCountryListResponse,
  QueryCountryDetailRequest,
  QueryCountryDetailResponse,
  QueryRegionListRequest,
  QueryRegionListResponse,
  SearchCountryRegionRequest,
  SearchCountryRegionResponse,
  GetPopularDestinationsRequest,
  GetPopularDestinationsResponse,
} from './types'
import type { BaseResponse } from '../types'

// API基础路径
const API_BASE_PATH = '/bss/app'

// ==================== 查询国家列表 ====================
/**
 * 查询国家列表
 * @param params 查询参数
 * @returns 国家列表
 */
export const queryCountryList = async (
  params: QueryCountryListRequest,
): Promise<BaseResponse<QueryCountryListResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryCountryListResponse>>({
    url: `${API_BASE_PATH}/QueryCountryList`,
    data: params,
  })
}

// ==================== 查询国家详情 ====================

/**
 * 查询国家详情
 * @param params 查询参数
 * @returns 国家详情
 */
export const queryCountryDetail = async (
  params: QueryCountryDetailRequest,
): Promise<BaseResponse<QueryCountryDetailResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryCountryDetailResponse>>({
    url: `${API_BASE_PATH}/QueryCountryDetail`,
    data: params,
  })
}

// ==================== 查询地区列表 ====================

/**
 * 查询地区列表
 * @param params 查询参数
 * @returns 地区列表
 */
export const queryRegionList = async (
  params: QueryRegionListRequest,
): Promise<BaseResponse<QueryRegionListResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryRegionListResponse>>({
    url: `${API_BASE_PATH}/QueryRegionList`,
    data: params,
  })
}

// ==================== 搜索国家地区 ====================

/**
 * 搜索国家地区
 * @param params 搜索参数
 * @returns 搜索结果
 */
export const searchCountryRegion = async (
  params: SearchCountryRegionRequest,
): Promise<BaseResponse<SearchCountryRegionResponse> | undefined> => {
  return await postJSON<BaseResponse<SearchCountryRegionResponse>>({
    url: `${API_BASE_PATH}/SearchCountryRegion`,
    data: params,
  })
}

// ==================== 获取热门目的地 ====================

/**
 * 获取热门目的地
 * @param params 查询参数
 * @returns 热门目的地列表
 */
export const getPopularDestinations = async (
  params: GetPopularDestinationsRequest,
): Promise<BaseResponse<GetPopularDestinationsResponse> | undefined> => {
  return await postJSON<BaseResponse<GetPopularDestinationsResponse>>({
    url: `${API_BASE_PATH}/noauth/dictionary/QueryKeywordList`,
    data: params,
  })
}

// ==================== 导出所有API ====================

export const countryApi = {
  queryCountryList,
  queryCountryDetail,
  queryRegionList,
  searchCountryRegion,
  getPopularDestinations,
}

// 默认导出
export default countryApi
