// 国家地区相关API类型定义
import { LangType, DestinationKeyword } from '@/api'
import type { BaseRequest, BaseResponse } from '../types'
// ==================== 国家信息 ====================

export interface CountryInfo {
  /** 国家编码 */
  countryCode: string
  /** 国家名称 */
  countryName: string
  /** ISO2编码 */
  iso2: string
  /** ISO3编码 */
  iso3?: string
  /** 国家电话区号 */
  phoneCode: string
  /** 国家货币编码 */
  currencyCode?: string
  /** 国家货币名称 */
  currencyName?: string
  /** 国家货币符号 */
  currencySymbol?: string
  /** 国家时区 */
  timezone?: string
  /** 国家大洲 */
  continent?: string
  /** 国家首都 */
  capital?: string
  /** 国家语言 */
  language?: string
  /** 国家国旗图标URL */
  flagUrl?: string
  /** 是否支持eSIM */
  supportEsim?: boolean
  /** 是否热门国家 */
  isPopular?: boolean
  /** 排序权重 */
  sortWeight?: number
  /** 国家状态 */
  status?: string
}

// ==================== 地区信息 ====================

export interface RegionInfo {
  /** 地区编码 */
  regionCode: string
  /** 地区名称 */
  regionName: string
  /** 所属国家编码 */
  countryCode: string
  /** 地区类型（省、州、市等） */
  regionType: string
  /** 父级地区编码 */
  parentRegionCode?: string
  /** 地区级别 */
  level: number
  /** 排序权重 */
  sortWeight?: number
  /** 地区状态 */
  status?: string
}

// ==================== 查询国家列表 ====================

export interface QueryCountryListRequest extends BaseRequest {
  /** 语言类型 */
  langType: LangType
  /** 是否只查询支持eSIM的国家 */
  esimOnly?: boolean
  /** 是否只查询热门国家 */
  popularOnly?: boolean
  /** 大洲过滤 */
  continent?: string
  /** 国家名称关键字（模糊查询） */
  keyword?: string
  /** 是否包含国旗图标 */
  includeFlag?: boolean
}

export interface QueryCountryListResponse {
  /** 国家列表 */
  countries: CountryInfo[]
  /** 热门国家列表 */
  popularCountries?: CountryInfo[]
  /** 按大洲分组的国家 */
  continentGroups?: {
    [continent: string]: CountryInfo[]
  }
}

// ==================== 查询国家详情 ====================

export interface QueryCountryDetailRequest extends BaseRequest {
  /** 国家编码或ISO2编码 */
  countryCode: string
  /** 语言类型 */
  langType: LangType
}

export interface QueryCountryDetailResponse extends CountryInfo {
  /** 国家下的地区列表 */
  regions?: RegionInfo[]
  /** 国家支持的运营商列表 */
  operators?: {
    /** 运营商编码 */
    operatorCode: string
    /** 运营商名称 */
    operatorName: string
    /** 网络类型 */
    networkType: string[]
  }[]
  /** 国家的套餐统计 */
  packageStats?: {
    /** 可用套餐数量 */
    totalPackages: number
    /** 最低价格（分） */
    minPrice: number
    /** 最高价格（分） */
    maxPrice: number
    /** 平均价格（分） */
    avgPrice: number
  }
}

// ==================== 查询地区列表 ====================

export interface QueryRegionListRequest extends BaseRequest {
  /** 国家编码 */
  countryCode: string
  /** 语言类型 */
  langType: LangType
  /** 父级地区编码 */
  parentRegionCode?: string
  /** 地区级别 */
  level?: number
  /** 地区类型 */
  regionType?: string
}

export interface QueryRegionListResponse {
  /** 地区列表 */
  regions: RegionInfo[]
}

// ==================== 搜索国家地区 ====================

export interface SearchCountryRegionRequest extends BaseRequest {
  /** 搜索关键字 */
  keyword: string
  /** 语言类型 */
  langType: LangType
  /** 搜索类型：country-只搜索国家，region-只搜索地区，all-搜索全部 */
  searchType?: 'country' | 'region' | 'all'
  /** 最大返回结果数 */
  maxResults?: number
}

export interface SearchCountryRegionResponse {
  /** 匹配的国家列表 */
  countries: CountryInfo[]
  /** 匹配的地区列表 */
  regions: RegionInfo[]
  /** 搜索建议 */
  suggestions?: string[]
}

// ==================== 获取热门目的地 ====================
export interface GetPopularDestinationsRequest extends BaseRequest {
  /** 语言类型 */
  langType: LangType
  /** 目的地类型 */
  type: DestinationKeyword
  /** 登录用户Id */
  loginCustomerId?: string
  /** 关键字 */
  keyword?: string
}

export interface PopularDestination {
  /** 关键字 */
  key: string
  /** 关键字对应的value */
  value: string
  /** 关键字对应value的类型 */
  keyType: 'COUNTRY' | 'REGION' | 'PACKAGE'
  /** COUNTRY-ios2，REGION-编码UN51，PACKAGE-null */
  iso2: string
  /** 国旗或图标URL */
  iconUrl?: string
}

/** 热门目的地列表 */
export type GetPopularDestinationsResponse = PopularDestination[]

// ==================== API函数类型定义 ====================

export type QueryCountryListAPI = (
  params: QueryCountryListRequest,
) => Promise<BaseResponse<QueryCountryListResponse>>
export type QueryCountryDetailAPI = (
  params: QueryCountryDetailRequest,
) => Promise<BaseResponse<QueryCountryDetailResponse>>
export type QueryRegionListAPI = (
  params: QueryRegionListRequest,
) => Promise<BaseResponse<QueryRegionListResponse>>
export type SearchCountryRegionAPI = (
  params: SearchCountryRegionRequest,
) => Promise<BaseResponse<SearchCountryRegionResponse>>
export type GetPopularDestinationsAPI = (
  params: GetPopularDestinationsRequest,
) => Promise<BaseResponse<GetPopularDestinationsResponse>>
