// 产品相关API类型定义
import {
  LangType,
  ChannelType,
  CurrencyType,
  SimCardType,
  AdFlag,
  Consecutive,
  PayAgreeFlag,
  AreaFlag,
  MccFlag,
  GoodsType,
  CategoryCode,
} from '@/api'
import type {
  PageData,
  BillingInfoVO,
  IconInfoVO,
  ExtendValVO,
  DeductionInfo,
  SensorsMap,
  BaseRequest,
  BaseResponse,
} from '../types'
// ==================== 销售品属性映射 ====================

export interface AttrMap {
  /** 描述 */
  pkDesc?: string
  /** 标题 */
  title?: string
  /** 流量大小 */
  flowSize?: string
  /** 有效期 */
  period?: number
  /** 流量包类型 */
  pkType?: string
  /** 流量阈值 */
  flowThreshold?: string
  /** 时区 */
  timeZone?: string
  /** 价格阈值（分） */
  balanceThreshold?: number
  /** 生效类型 */
  effType?: string
  /** 周期单位 */
  periodUnit?: string
  /** 优惠类型 */
  discType?: string
  /** 在线数 */
  onlineNum?: string
  /** 失效类型 */
  expType?: string
  /** 备注 */
  remark?: string
  /** 软卡类型 */
  simCardType?: SimCardType
  /** 充值面额赠送金额 */
  giveAmount?: number
  /** 设备类型 */
  tmlType?: string
  /** 颜色 */
  colors?: string
  /** 在线用户数限制 */
  onlineLimit?: string
  /** 广告标识 */
  adFlag?: AdFlag
  /** 时长方式 */
  periodMode?: string
  /** 剩余流量阈值 */
  threshold?: string
  /** 销售品激活期限 */
  activeDeadline?: number
  /** 是否连续 */
  consecutive?: Consecutive
  /** 失效时间（Unix时间戳，毫秒） */
  expiryDate?: number
  /** 最大购买数量 */
  maxCount?: string
  /** 是否支持签约 */
  payAgreeFlag?: PayAgreeFlag
  /** 流量区域标签 */
  areaFlag?: AreaFlag
  /** 本地流量 */
  packageLocal?: string
  /** 本地流量描述 */
  packageLocalRemark?: string
  /** 全球流量标题 */
  packageGlobal?: string
  /** 全球流量标题描述 */
  packageGlobalRemark?: string
  /** 专属套餐 */
  packageVip?: string
  /** 专属套餐描述 */
  packageVipRemark?: string
  /** 优惠券折扣 */
  discountCoupon?: string
  /** 优惠券折扣描述 */
  discountCouponRemark?: string
  /** 会员日折扣 */
  discountVipData?: string
  /** 会员日折扣描述 */
  discountVipDataRemark?: string
  /** 双倍积分标题 */
  pointDouble?: string
  /** 双倍积分描述 */
  pointDoubleRemark?: string
  /** 剩余流量专属 */
  pointConvert?: string
  /** 剩余流量专属描述 */
  pointConvertRemark?: string
  /** 最多可省（元） */
  saveUpTo?: number
  /** 苹果产品ID */
  appleProductId?: string
  /** 豁免偏移小时量 */
  exemptionPeriod?: number
  /** 补网运营商 */
  networkOperator?: string
  /** 关联编码 */
  relationCode?: string
  /** 附属产品的服务类型 */
  serviceType?: string
  /** 定价类型 */
  priceType?: string
  /** 定价说明 */
  priceRemark?: string
  /** 开通时效说明 */
  openRemark?: string
  /** 可购买设备组 */
  buyTerminalType?: string
  /** 场景类型 */
  sceneList?: string
  /** 可购买主卡类型 */
  availableCardType?: string
  /** eSIM下载次数 */
  eSIMDownloadTimes?: string
}

// ==================== 销售品查询 ====================

export interface QueryOfferListRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** MVNO编码 */
  mvnoCode: string
  /** 语言类型 */
  langType: LangType
  /** 可使用渠道 */
  channelType: ChannelType
  /** 开始时间，GMT0时间戳 */
  bgTime?: number
  /** 结束时间，GMT0时间戳 */
  endTime?: number
  /** 当前页，默认为1 */
  currentPage?: number
  /** 每页条数，默认为100 */
  perPageCount?: number
  /** 服务国家 */
  mcc?: string
  /** 国家标识，WHITE:白名单 BLACK:黑名单 */
  mccFlag?: MccFlag
  /** 销售品类型 */
  goodsType?: GoodsType
  /** 销售品编码 */
  goodsCode?: string
  /** 类目编码 */
  categoryCode?: CategoryCode
  /** 类目编码集合 */
  categoryCodes?: string[]
  /** 可用设备类型 */
  terminalType?: string
  /** 是否查询营销 */
  queryPromotionFlag?: boolean
  /** 是否查询 */
  diyFlag?: number
  /** 是否出于计费信息 */
  queryLimitFlag?: boolean
  /** 查询条件是否忽略渠道 */
  ignoreChannelType?: boolean
  /** 扩展参数 */
  params?: object
  /** 机构Id */
  orgId?: string
  /** 是否根据数据排序 */
  sortFlag?: number
  /** 过滤条件 */
  extendValueMap?: object
  /** 表示套餐可购买套餐 */
  buyTerminalType?: string
  /** 本地服务还是全球服务 */
  areaFlag?: AreaFlag
  /** 销售品属性列表 */
  goodsExtendAttrs?: string[]
  /** 是否过滤流量套餐 */
  showFlowFlag?: number
  /** 过滤电话标识标识 */
  filterSimFlag?: string
  /** 资源位id */
  section_id?: string
  /** 联系方式 */
  mainCardType?: 'OTA' | 'OTA-eSIM' | 'OTA-eSIM-BLESIM'
}

export interface OfferItem {
  /** 销售品ID */
  goodsId?: string
  /** 销售品编码 */
  goodsCode?: string
  /** 销售品名称 */
  goodsName?: string
  /** 销售品价格, 单位：分 */
  goodsPrice?: number
  /** 服务国家名称列表 */
  mccList?: string[]
  /** 服务国家简码列表 */
  iso2List?: string[]
  /** 国家标识，WHITE:白名单 BLACK:黑名单 */
  mccFlag?: MccFlag
  /** 创建时间 */
  createTime?: number
  /** 流量大小，单位MB，保留两位小数 */
  flowByte?: number
  /** 有效期，例如7表示一周套餐 */
  period?: string
  /** 是否可以取消 */
  cancelFlag?: boolean
  /** 有效期单位，例如DAY，MONTH */
  periodUnit?: string
  /** 销售品类型 */
  goodsType?: GoodsType
  /** 货币类型 */
  currencyType?: CurrencyType
  /** 类目编码，例如CXTC：畅想套餐；LLTC：流量套餐等 */
  categoryCode?: CategoryCode
  /** 销售品扩展属性 */
  attrMap?: AttrMap
  /** 是否有促销活动 */
  promotionFlag?: boolean
  /** 促销活动编码 */
  promotionActivityCode?: string
  /** 折扣后价格 */
  discountPrice?: number
  /** 是否多份连续 */
  consecutive?: Consecutive
  /** 归属设备组 */
  terminalType?: string
  /** 是否支持签约 */
  payAgreeFlag?: number
  /** 连续畅享活动支持连续才返回 */
  deductionInfo?: DeductionInfo
  /** 角标信息 */
  cornerMark?: object
  /** 活动标签信息 */
  actTags?: object
  /** 是否会员活动；1-是，其他-否 */
  vipAct?: string
  /** 计费信息 */
  billingInfos?: BillingInfoVO[]
  /** 图标信息 */
  iconInfos?: IconInfoVO[]
  /** 扩展属性信息 */
  extendValVos?: ExtendValVO[]
  /** 传感器映射 */
  sensorsMap?: SensorsMap
}

export interface QueryOfferListResponse extends PageData<OfferItem> {}

// ==================== API函数类型定义 ====================

export type QueryOfferListAPI = (
  params: QueryOfferListRequest,
) => Promise<BaseResponse<QueryOfferListResponse>>
