// 产品相关API实现
import { postJSON } from '@/utils/fetch'
import type { QueryOfferListRequest, QueryOfferListResponse } from './types'
import type { BaseResponse } from '../types'

// API基础路径
const API_BASE_PATH = '/bss/app/noauth'

// ==================== 销售品查询 ====================

/**
 * 查询销售品列表
 * @param params 查询参数
 * @returns 销售品列表
 */
export const queryOfferList = async (
  params: QueryOfferListRequest,
): Promise<BaseResponse<QueryOfferListResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryOfferListResponse>>({
    url: `${API_BASE_PATH}/QueryOfferList`,
    data: params,
  })
}

// ==================== 导出所有API ====================

export const productApi = {
  queryOfferList,
}

// 默认导出
export default productApi
