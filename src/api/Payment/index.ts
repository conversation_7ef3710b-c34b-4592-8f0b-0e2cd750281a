// 支付相关API实现
import { postJSON } from '@/utils/fetch'
import type {
  CreatePaymentRequest,
  CreatePaymentResponse,
  QueryPaymentStatusRequest,
  QueryPaymentStatusResponse,
  QueryPaymentMethodsRequest,
  QueryPaymentMethodsResponse,
  CancelPaymentRequest,
  ApplyRefundRequest,
  ApplyRefundResponse,
  QueryRefundStatusRequest,
  QueryRefundStatusResponse,
} from './types'
import type { BaseResponse } from '../types'

// API基础路径
const API_BASE_PATH = '/bss/app/payment'

// ==================== 创建支付 ====================

/**
 * 创建支付
 * @param params 创建支付参数
 * @returns 支付创建结果
 */
export const createPayment = async (
  params: CreatePaymentRequest,
): Promise<BaseResponse<CreatePaymentResponse> | undefined> => {
  return await postJSON<BaseResponse<CreatePaymentResponse>>({
    url: `${API_BASE_PATH}/CreatePayment`,
    data: params,
  })
}

// ==================== 查询支付状态 ====================

/**
 * 查询支付状态
 * @param params 查询参数
 * @returns 支付状态信息
 */
export const queryPaymentStatus = async (
  params: QueryPaymentStatusRequest,
): Promise<BaseResponse<QueryPaymentStatusResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryPaymentStatusResponse>>({
    url: `${API_BASE_PATH}/QueryPaymentStatus`,
    data: params,
  })
}

// ==================== 查询支付方式列表 ====================

/**
 * 查询支付方式列表
 * @param params 查询参数
 * @returns 支付方式列表
 */
export const queryPaymentMethods = async (
  params: QueryPaymentMethodsRequest,
): Promise<BaseResponse<QueryPaymentMethodsResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryPaymentMethodsResponse>>({
    url: `${API_BASE_PATH}/QueryPaymentMethods`,
    data: params,
  })
}

// ==================== 取消支付 ====================

/**
 * 取消支付
 * @param params 取消支付参数
 * @returns 取消结果
 */
export const cancelPayment = async (
  params: CancelPaymentRequest,
): Promise<BaseResponse<string> | undefined> => {
  return await postJSON<BaseResponse<string>>({
    url: `${API_BASE_PATH}/CancelPayment`,
    data: params,
  })
}

// ==================== 申请退款 ====================

/**
 * 申请退款
 * @param params 退款申请参数
 * @returns 退款申请结果
 */
export const applyRefund = async (
  params: ApplyRefundRequest,
): Promise<BaseResponse<ApplyRefundResponse> | undefined> => {
  return await postJSON<BaseResponse<ApplyRefundResponse>>({
    url: `${API_BASE_PATH}/ApplyRefund`,
    data: params,
  })
}

// ==================== 查询退款状态 ====================

/**
 * 查询退款状态
 * @param params 查询参数
 * @returns 退款状态信息
 */
export const queryRefundStatus = async (
  params: QueryRefundStatusRequest,
): Promise<BaseResponse<QueryRefundStatusResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryRefundStatusResponse>>({
    url: `${API_BASE_PATH}/QueryRefundStatus`,
    data: params,
  })
}

// ==================== 导出所有API ====================

export const paymentApi = {
  createPayment,
  queryPaymentStatus,
  queryPaymentMethods,
  cancelPayment,
  applyRefund,
  queryRefundStatus,
}

// 默认导出
export default paymentApi
