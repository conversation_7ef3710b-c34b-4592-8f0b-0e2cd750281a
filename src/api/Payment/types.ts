// 支付相关API类型定义
import { LangType, CurrencyType, PayMethod } from '@/api'
import type { BaseRequest, BaseResponse } from '../types'
// ==================== 支付信息 ====================

export interface PaymentInfo {
  /** 支付ID */
  paymentId: string
  /** 订单号 */
  orderSN: string
  /** 支付方式 */
  payMethod: PayMethod
  /** 支付金额（分） */
  amount: number
  /** 货币类型 */
  currencyType: CurrencyType
  /** 支付状态 */
  status: string
  /** 支付时间 */
  payTime?: number
  /** 创建时间 */
  createTime: number
  /** 支付描述 */
  description?: string
  /** 支付渠道 */
  payChannel?: string
  /** 第三方支付流水号 */
  thirdPartyTransactionId?: string
  /** 支付失败原因 */
  failureReason?: string
}

// ==================== 支付方式信息 ====================

export interface PaymentMethodInfo {
  /** 支付方式编码 */
  payMethod: PayMethod
  /** 支付方式名称 */
  payMethodName: string
  /** 支付方式描述 */
  description?: string
  /** 支付方式图标URL */
  iconUrl?: string
  /** 是否可用 */
  available: boolean
  /** 支持的货币类型 */
  supportedCurrencies: CurrencyType[]
  /** 最小支付金额（分） */
  minAmount?: number
  /** 最大支付金额（分） */
  maxAmount?: number
  /** 手续费率（百分比） */
  feeRate?: number
  /** 固定手续费（分） */
  fixedFee?: number
  /** 排序权重 */
  sortWeight?: number
}

// ==================== 创建支付 ====================

export interface CreatePaymentRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 订单号 */
  orderSN: string
  /** 支付方式 */
  payMethod: PayMethod
  /** 支付金额（分） */
  amount: number
  /** 货币类型 */
  currencyType: CurrencyType
  /** 语言类型 */
  langType: LangType
  /** 支付描述 */
  description?: string
  /** 支付成功回调URL */
  successUrl?: string
  /** 支付失败回调URL */
  failureUrl?: string
  /** 支付取消回调URL */
  cancelUrl?: string
  /** 异步通知URL */
  notifyUrl?: string
  /** 客户端IP地址 */
  clientIp?: string
  /** 设备信息 */
  deviceInfo?: string
  /** 扩展参数 */
  extendParams?: object
}

export interface CreatePaymentResponse {
  /** 支付ID */
  paymentId: string
  /** 支付状态 */
  status: string
  /** 支付URL（用于跳转到第三方支付页面） */
  paymentUrl?: string
  /** 支付二维码（用于扫码支付） */
  qrCode?: string
  /** 支付表单HTML（用于自动提交表单） */
  paymentForm?: string
  /** 支付参数（用于客户端SDK） */
  paymentParams?: object
  /** 支付过期时间 */
  expireTime?: number
}

// ==================== 查询支付状态 ====================

export interface QueryPaymentStatusRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 支付ID */
  paymentId: string
}

export interface QueryPaymentStatusResponse extends PaymentInfo {}

// ==================== 查询支付方式列表 ====================

export interface QueryPaymentMethodsRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 语言类型 */
  langType: LangType
  /** 货币类型 */
  currencyType: CurrencyType
  /** 支付金额（分），用于过滤支持该金额的支付方式 */
  amount?: number
  /** 国家编码，用于过滤该国家支持的支付方式 */
  countryCode?: string
}

export interface QueryPaymentMethodsResponse {
  /** 支付方式列表 */
  paymentMethods: PaymentMethodInfo[]
  /** 推荐支付方式 */
  recommendedMethods?: PaymentMethodInfo[]
}

// ==================== 取消支付 ====================

export interface CancelPaymentRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 支付ID */
  paymentId: string
  /** 取消原因 */
  cancelReason?: string
}

// 取消支付响应使用通用BusinessResponse<string>

// ==================== 申请退款 ====================

export interface ApplyRefundRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 支付ID */
  paymentId: string
  /** 退款金额（分），不传则全额退款 */
  refundAmount?: number
  /** 退款原因 */
  refundReason: string
  /** 语言类型 */
  langType: LangType
}

export interface ApplyRefundResponse {
  /** 退款申请ID */
  refundId: string
  /** 退款状态 */
  status: string
  /** 退款金额（分） */
  refundAmount: number
  /** 预计退款到账时间 */
  estimatedArrivalTime?: number
  /** 退款说明 */
  description?: string
}

// ==================== 查询退款状态 ====================

export interface QueryRefundStatusRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 退款申请ID */
  refundId: string
}

export interface RefundInfo {
  /** 退款申请ID */
  refundId: string
  /** 支付ID */
  paymentId: string
  /** 订单号 */
  orderSN: string
  /** 退款金额（分） */
  refundAmount: number
  /** 退款状态 */
  status: string
  /** 退款原因 */
  refundReason: string
  /** 申请时间 */
  applyTime: number
  /** 处理时间 */
  processTime?: number
  /** 退款完成时间 */
  completeTime?: number
  /** 退款失败原因 */
  failureReason?: string
  /** 第三方退款流水号 */
  thirdPartyRefundId?: string
}

export interface QueryRefundStatusResponse extends RefundInfo {}

// ==================== API函数类型定义 ====================

export type CreatePaymentAPI = (
  params: CreatePaymentRequest,
) => Promise<BaseResponse<CreatePaymentResponse>>
export type QueryPaymentStatusAPI = (
  params: QueryPaymentStatusRequest,
) => Promise<BaseResponse<QueryPaymentStatusResponse>>
export type QueryPaymentMethodsAPI = (
  params: QueryPaymentMethodsRequest,
) => Promise<BaseResponse<QueryPaymentMethodsResponse>>
export type CancelPaymentAPI = (params: CancelPaymentRequest) => Promise<BaseResponse<string>>
export type ApplyRefundAPI = (
  params: ApplyRefundRequest,
) => Promise<BaseResponse<ApplyRefundResponse>>
export type QueryRefundStatusAPI = (
  params: QueryRefundStatusRequest,
) => Promise<BaseResponse<QueryRefundStatusResponse>>
