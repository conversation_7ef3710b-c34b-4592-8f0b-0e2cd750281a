// ESimStore API 通用类型定义
import { PeriodUnit } from '@/api'
// ==================== 基础类型 ====================

/** 基础请求接口 */
export interface BaseRequest {
  /** 请求流水号唯一 - code(5位) + yyyymmddhhmmss(14位时间序列) +6位序列 */
  streamNo: string
  /** 合作伙伴编码 */
  partnerCode: string
}

// 标准业务响应接口
export interface BaseResponse<T = any> {
  /** 请求流水号唯一 */
  streamNo: string
  /** 结果编码 - 00000000：表示成功 */
  resultCode: string
  /** 结果描述 */
  resultDesc: string
  /** 返回对象 */
  data: T
}

// 分页数据接口
export interface PageData<T = any> {
  /** 每页条数 */
  perPageCount: number
  /** 当前页 */
  currentPage: number
  /** 总条数 */
  totalCount: number
  /** 总页数 */
  totalPageCount: number
  /** 返回的数据列表 */
  dataList: T[]
}

// ==================== 通用对象类型 ====================

/** 传感器映射 */
export interface SensorsMap {
  /** 资源位的策略ID */
  strategy_id: string
  /** 策略类型 */
  strategy_type: string
}

/** 连续畅享活动信息 */
export interface DeductionInfo {
  /** 连续自动续费首阶段价格（分） */
  autoRenewFirstPrice: number
  /** 连续自动续费价格（分） */
  autoRenewPrice: number
  /** 连续畅想活动的code */
  autoRenewActCode: string
}

/** 计费信息 */
export interface BillingInfoVO {
  /** 计费量, 高速流量显示值或者第一档限速流量（单位M） */
  flow: number
  /** 计费周期 */
  period: number
  /** 计费周期单位 */
  periodUnit: PeriodUnit
}

/** 图标信息 */
export interface IconInfoVO {
  /** 属性值对应的id */
  id: string
  /** 销售品ID */
  goodsId: string
  /** 销售品编码 */
  goodsCode: string
  /** 属性编码 */
  attrCode: string
  /** 属性名称 */
  attrName: string
  /** 属性值 */
  attrValue: string
  /** 语言类型 */
  langType: string
  /** 备注 */
  remark: string
}

/** 扩展属性 */
export interface ExtendValVO {
  /** 属性编码 */
  attrCode: string
  /** 属性名称 */
  attrName: string
  /** 属性值列表 */
  attrValueList: string[]
}

// 兼容旧版本的通用响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}
