import { get } from '@/utils/fetch.ts'

export namespace CurrentUserAPI {
  export interface UserCacheDto {
    /**
     * 头像
     */
    avatar?: string
    /**
     * 所属城市
     */
    city?: string
    /**
     * 所属部门
     */
    depts?: DeptCacheDto[]
    /**
     * 邮箱
     */
    email?: string
    /**
     * 工号
     */
    employeeNo?: string
    /**
     * 员工类型：1：正式员工；2：实习生；3：外包；4：劳务；5：顾问
     */
    employeeType?: number
    /**
     * 全称
     */
    fullName?: string
    /**
     * 性别(0:保密；1，男；2，女)
     */
    gender?: number
    /**
     * id
     */
    id?: number
    /**
     * 职位
     */
    jobTitle?: string
    /**
     * 直属领导id
     */
    leaderUserId?: number
    /**
     * 直属领导uid
     */
    leaderUserUid?: string
    /**
     * 手机号
     */
    mobile?: string
    /**
     * 手机号国家码
     */
    mobileCountryCode?: string
    /**
     * 名称
     */
    name?: string
    /**
     * 用户来源：FEISHU-飞书；MANUAL-手动添加
     */
    source?: string
    /**
     * 员工状态：0：正常；1：未加入；2：冻结；3：主动退出；4：离职
     */
    status?: number
    /**
     * 唯一标识（取飞书union_id）
     */
    uid?: string
  }

  export interface DeptCacheDto {
    /**
     * id
     */
    id?: number
    /**
     * 部门leader id
     */
    leaderUserId?: number
    /**
     * 部门leader uid
     */
    leaderUserUid?: string
    /**
     * 部门名称
     */
    name?: string
    /**
     * 排序
     */
    order?: number
    /**
     * 父部门id
     */
    parentId?: number
    /**
     * 父部门uid
     */
    parentUid?: string
    /**
     * 来源：FEISHU-飞书；MANUAL-手动添加
     */
    source?: string
    /**
     * 部门类型：REAL:实体部门；DUMMY-虚拟部门
     */
    type?: string
    /**
     * 唯一标识
     */
    uid?: string
  }
}
export function getUserInfo() {
  return get({
    url: '/api/user/current',
  })
}
