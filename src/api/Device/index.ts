// 设备相关API实现
import { postJSON } from '@/utils/fetch'
import type {
  QueryDeviceListRequest,
  QueryDeviceListResponse,
  QueryDeviceDetailRequest,
  QueryDeviceDetailResponse,
  QueryDevicePackagesRequest,
  QueryDevicePackagesResponse,
  ActivateDeviceRequest,
  ActivateDeviceResponse,
  RenameDeviceRequest,
  DeleteDeviceRequest,
} from './types'
import type { BaseResponse } from '../types'

// API基础路径
const API_BASE_PATH = '/bss/app/device'

// ==================== 设备查询 ====================

/**
 * 查询设备列表
 * @param params 查询参数
 * @returns 设备列表
 */
export const queryDeviceList = async (
  params: QueryDeviceListRequest,
): Promise<BaseResponse<QueryDeviceListResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryDeviceListResponse>>({
    url: `${API_BASE_PATH}/QueryDeviceList`,
    data: params,
  })
}

// ==================== 设备详情查询 ====================

/**
 * 查询设备详情
 * @param params 查询参数
 * @returns 设备详情
 */
export const queryDeviceDetail = async (
  params: QueryDeviceDetailRequest,
): Promise<BaseResponse<QueryDeviceDetailResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryDeviceDetailResponse>>({
    url: `${API_BASE_PATH}/QueryDeviceDetail`,
    data: params,
  })
}

// ==================== 设备流量包查询 ====================

/**
 * 查询设备流量包
 * @param params 查询参数
 * @returns 设备流量包列表
 */
export const queryDevicePackages = async (
  params: QueryDevicePackagesRequest,
): Promise<BaseResponse<QueryDevicePackagesResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryDevicePackagesResponse>>({
    url: `${API_BASE_PATH}/QueryDevicePackages`,
    data: params,
  })
}

// ==================== 设备激活 ====================

/**
 * 激活设备
 * @param params 激活参数
 * @returns 激活结果
 */
export const activateDevice = async (
  params: ActivateDeviceRequest,
): Promise<BaseResponse<ActivateDeviceResponse> | undefined> => {
  return await postJSON<BaseResponse<ActivateDeviceResponse>>({
    url: `${API_BASE_PATH}/ActivateDevice`,
    data: params,
  })
}

// ==================== 设备重命名 ====================

/**
 * 重命名设备
 * @param params 重命名参数
 * @returns 重命名结果
 */
export const renameDevice = async (
  params: RenameDeviceRequest,
): Promise<BaseResponse<string> | undefined> => {
  return await postJSON<BaseResponse<string>>({
    url: `${API_BASE_PATH}/RenameDevice`,
    data: params,
  })
}

// ==================== 设备删除 ====================

/**
 * 删除设备
 * @param params 删除参数
 * @returns 删除结果
 */
export const deleteDevice = async (
  params: DeleteDeviceRequest,
): Promise<BaseResponse<string> | undefined> => {
  return await postJSON<BaseResponse<string>>({
    url: `${API_BASE_PATH}/DeleteDevice`,
    data: params,
  })
}

// ==================== 导出所有API ====================

export const deviceApi = {
  queryDeviceList,
  queryDeviceDetail,
  queryDevicePackages,
  activateDevice,
  renameDevice,
  deleteDevice,
}

// 默认导出
export default deviceApi
