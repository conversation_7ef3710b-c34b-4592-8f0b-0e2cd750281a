// 设备相关API类型定义
import { LangType, ChannelType, CurrencyType, PeriodUnit } from '@/api'
import type { BaseRequest, BaseResponse, PageData } from '../types'
// ==================== 设备信息 ====================

export interface DeviceInfo {
  /** 设备ID */
  deviceId: string
  /** 设备名称 */
  deviceName: string
  /** 设备IMEI */
  imei: string
  /** 设备类型 */
  deviceType: string
  /** 设备状态 */
  status: string
  /** 创建时间 */
  createTime: number
  /** 最后更新时间 */
  updateTime: number
  /** 设备描述 */
  description?: string
  /** 设备图片URL */
  imageUrl?: string
  /** 设备版本 */
  version?: string
  /** 设备序列号 */
  serialNumber?: string
  /** 设备MAC地址 */
  macAddress?: string
  /** 设备IP地址 */
  ipAddress?: string
  /** 设备位置信息 */
  location?: string
  /** 设备所属用户ID */
  userId?: string
  /** 设备所属组织ID */
  orgId?: string
}

// ==================== 流量包信息 ====================

export interface PackageInfo {
  /** 流量包ID */
  packageId: string
  /** 流量包名称 */
  packageName: string
  /** 流量包类型 */
  packageType: string
  /** 总流量（MB） */
  totalFlow: number
  /** 已使用流量（MB） */
  usedFlow: number
  /** 剩余流量（MB） */
  remainingFlow: number
  /** 生效时间 */
  effectiveTime: number
  /** 失效时间 */
  expireTime: number
  /** 流量包状态 */
  status: string
  /** 创建时间 */
  createTime: number
  /** 服务国家列表 */
  serviceCountries: string[]
  /** 流量包描述 */
  description?: string
  /** 周期 */
  period?: number
  /** 周期单位 */
  periodUnit?: PeriodUnit
  /** 价格（分） */
  price?: number
  /** 货币类型 */
  currencyType?: CurrencyType
}

// ==================== 设备查询 ====================

export interface QueryDeviceListRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 语言类型 */
  langType: LangType
  /** 渠道类型 */
  channelType: ChannelType
  /** 设备类型 */
  deviceType?: string
  /** 设备状态 */
  status?: string
  /** 当前页，默认为1 */
  currentPage?: number
  /** 每页条数，默认为20 */
  perPageCount?: number
  /** 开始时间，GMT0时间戳 */
  startTime?: number
  /** 结束时间，GMT0时间戳 */
  endTime?: number
  /** 设备名称（模糊查询） */
  deviceName?: string
  /** 设备IMEI */
  imei?: string
}

export interface QueryDeviceListResponse extends PageData<DeviceInfo> {}

// ==================== 设备详情查询 ====================

export interface QueryDeviceDetailRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 设备ID */
  deviceId: string
  /** 语言类型 */
  langType: LangType
}

export interface QueryDeviceDetailResponse extends DeviceInfo {
  /** 关联的流量包列表 */
  packages: PackageInfo[]
  /** 设备配置信息 */
  configuration?: object
  /** 设备统计信息 */
  statistics?: {
    /** 总使用流量（MB） */
    totalUsedFlow: number
    /** 本月使用流量（MB） */
    monthlyUsedFlow: number
    /** 在线时长（小时） */
    onlineHours: number
    /** 连接次数 */
    connectionCount: number
  }
}

// ==================== 设备流量包查询 ====================

export interface QueryDevicePackagesRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 设备ID */
  deviceId: string
  /** 语言类型 */
  langType: LangType
  /** 流量包状态 */
  status?: string
  /** 当前页，默认为1 */
  currentPage?: number
  /** 每页条数，默认为20 */
  perPageCount?: number
}

export interface QueryDevicePackagesResponse extends PageData<PackageInfo> {}

// ==================== 设备激活 ====================

export interface ActivateDeviceRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 设备IMEI */
  imei: string
  /** 激活码 */
  activationCode: string
  /** 语言类型 */
  langType: LangType
  /** 渠道类型 */
  channelType: ChannelType
  /** 设备名称 */
  deviceName?: string
  /** 设备描述 */
  description?: string
}

export interface ActivateDeviceResponse {
  /** 设备ID */
  deviceId: string
  /** 激活状态 */
  status: string
  /** 激活时间 */
  activationTime: number
  /** 激活结果描述 */
  message: string
}

// ==================== 设备重命名 ====================

export interface RenameDeviceRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 设备ID */
  deviceId: string
  /** 新设备名称 */
  newDeviceName: string
  /** 语言类型 */
  langType: LangType
}

// 设备重命名响应使用通用BusinessResponse<string>

// ==================== 设备删除 ====================

export interface DeleteDeviceRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 设备ID */
  deviceId: string
  /** 语言类型 */
  langType: LangType
}

// 设备删除响应使用通用BusinessResponse<string>

// ==================== API函数类型定义 ====================

export type QueryDeviceListAPI = (
  params: QueryDeviceListRequest,
) => Promise<BaseResponse<QueryDeviceListResponse>>
export type QueryDeviceDetailAPI = (
  params: QueryDeviceDetailRequest,
) => Promise<BaseResponse<QueryDeviceDetailResponse>>
export type QueryDevicePackagesAPI = (
  params: QueryDevicePackagesRequest,
) => Promise<BaseResponse<QueryDevicePackagesResponse>>
export type ActivateDeviceAPI = (
  params: ActivateDeviceRequest,
) => Promise<BaseResponse<ActivateDeviceResponse>>
export type RenameDeviceAPI = (params: RenameDeviceRequest) => Promise<BaseResponse<string>>
export type DeleteDeviceAPI = (params: DeleteDeviceRequest) => Promise<BaseResponse<string>>
