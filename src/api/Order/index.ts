// 订单相关API实现
import { postJSON } from '@/utils/fetch'
import type {
  CreateOrderRequest,
  CreateOrderResponse,
  DoPayByBalanceRequest,
  QueryOrderListRequest,
  QueryOrderListResponse,
} from './types'
import type { BaseResponse } from '../types'

// API基础路径
const API_BASE_PATH = '/bss/app/order'

// ==================== 创建订单 ====================

/**
 * 创建订单
 * @param params 创建订单参数
 * @returns 订单创建结果
 */
export const createOrder = async (
  params: CreateOrderRequest,
): Promise<BaseResponse<CreateOrderResponse> | undefined> => {
  return await postJSON<BaseResponse<CreateOrderResponse>>({
    url: `${API_BASE_PATH}/CreateOrder`,
    data: params,
  })
}

// ==================== 余额支付 ====================

/**
 * 使用余额支付订单
 * @param params 支付参数
 * @returns 支付结果
 */
export const doPayByBalance = async (
  params: DoPayByBalanceRequest,
): Promise<BaseResponse<string> | undefined> => {
  return await postJSON<BaseResponse<string>>({
    url: `${API_BASE_PATH}/DoPayByBalance`,
    data: params,
  })
}

// ==================== 导出所有API ====================

// ==================== 查询订单列表 ====================

/**
 * 查询订单列表
 * @param params 查询参数
 * @returns 订单列表
 */
export const queryOrderList = async (
  params: QueryOrderListRequest,
): Promise<BaseResponse<QueryOrderListResponse> | undefined> => {
  return await postJSON<BaseResponse<QueryOrderListResponse>>({
    url: `${API_BASE_PATH}/QueryOrderList`,
    data: params,
  })
}

export const orderApi = {
  createOrder,
  doPayByBalance,
  queryOrderList,
}

// 默认导出
export default orderApi
