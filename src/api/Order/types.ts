// 订单相关API类型定义
import { LangType, ChannelType, CurrencyType, PayMethod, OrderType, MainCardType } from '@/api'
import type { BaseRequest, BaseResponse } from '../types'
// ==================== 订单商品信息 ====================

export interface GoodsVO {
  /** 销售品ID */
  goodsId: string
  /** 数量 */
  quantity: number
  /** 生效时间（GMT0时间戳），自定义生效、预约生失效类型的销售品必传 */
  effectiveTime: number
  /** 失效时间（GMT0时间戳），自定义生效、预约生失效类型的销售品必传 */
  expireTime: number
  /** DIY套餐国家地区列表，适用于DIY套餐 */
  mccList?: string[]
  /** 流量大小，适用于DIY套餐，单位M */
  flowSize?: number
}

export interface IntegralGoodVO {
  /** 积分商品ID，非销售品ID */
  integralGoodId: string
  /** 数量，大于0 */
  quantity: number
}

export interface DeliveryInfo {
  /** 收件人姓名 */
  recipient?: string
  /** 省 */
  province?: string
  /** 市 */
  city?: string
  /** 手机号码 */
  phone?: string
  /** 收件人详细地址 */
  address?: string
  /** 发货备注 */
  remark: string
}

export interface UpgradeParam {
  /** 基础抵扣金额（单位：分） */
  upgradeAmount: number
}

export interface DeductInfo {
  /** 周期单位（自然天：d，自然月：m） */
  deductPeriodUnit: string
  /** 周期数，与周期单位组合使用 */
  deductPeriodNumber: number
  /** 每次扣款的最大金额，单位分 */
  deductAmount: number
  /** 下次发起扣款的时间，格式：YYYY-MM-DD */
  deductFirstTime: string
}

// ==================== 创建订单 ====================

export interface CreateOrderRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerld: string
  /** 用户编码 */
  userCode?: string
  /** 用户下单时选用的语言 */
  langType?: LangType
  /** 可使用渠道 */
  channelType: ChannelType
  /** 订单类型 */
  orderType: keyof typeof OrderType
  /** 订单项列表, 非积分支付必传 */
  goodsList?: GoodsVO[]
  /** 积分商品列表, 积分支付必传 */
  integralGoods?: IntegralGoodVO[]
  /** 订单描述 */
  orderMark?: string
  /** 支付货币类型, 非积分支付必传 */
  currencyType?: CurrencyType
  /** 支付方式 */
  payMethod?: PayMethod
  /** 邮寄相关信息，在orderType='BUYDEVICE'时需要邮寄时填写 */
  deliveryInfo?: DeliveryInfo
  /** 促销码，使用促销码一次只能购买一个套餐 */
  promotionCode?: string
  /** promotionCode对应的促销实例Id */
  promotionInstId?: string
  /** 促销码类型，如INST(普通优惠券)、VIP(会员活动优惠券) */
  promotionInstType?: string
  /** 发票抬头 */
  invoiceTitle?: string
  /** 发票明细，包含统一编号和发票明细 */
  invoiceDetail?: string
  /** 家庭子账号设备imei */
  imei?: string
  /** 设备imei */
  buyImei?: string
  /** 连续畅想签约字段，1：支持连续畅想签约，针对LXCX套餐，0或其他不支持 */
  payAgreeFlag?: string
  /** 图片验证码ID */
  randomId?: string
  /** 图片验证码 */
  randomCode?: string
  /** 活动参数，用于指定参与活动；支持多个活动编码，之间用##分隔 */
  actCode?: string
  /** 营销活动、优惠券是否互斥，默认true */
  mutex?: boolean
  /** OTASIM卡ICCID */
  iccid?: string
  /** eSim卡订单二维码收件箱；eSim卡订单时必填，用于推送二维码 */
  email?: string
  /** 优惠券和营销会员权益优惠与会员商品优惠之间的关系，默认false，表示可叠加 */
  vipRightsMutex?: boolean
  /** 场景化高级服务升级标识，1：升级，0：不升级，默认为0 */
  upgradeFlag?: '1' | '0'
  /** 场景化高级服务升级基础套餐抵扣信息 */
  upgradeParam?: UpgradeParam
  /** 主卡类型；当类目为OTASIM_PACKAGE时才携带此参数 */
  mainCardType?: MainCardType
}

export interface CreateOrderResponse {
  /** 订单SN */
  orderSN: string
  /** 支付金额 */
  amount: number
  /** 付款货币类型 */
  currencyType: CurrencyType
  /** 订单描述 */
  orderDesc?: string
  /** 订单Id */
  orderId: string
  /** 扣减信息对象 */
  deductInfo?: DeductInfo
}

// ==================== 余额支付 ====================

export interface DoPayByBalanceRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** MVNO编码 */
  mvnoCode: string
  /** 订单流水号 */
  orderSN: string
  /** 订单描述，用于支付页面展示 */
  orderDesc: string
}

// 余额支付响应使用通用BusinessResponse<string>

// ==================== 查询订单列表 ====================

export interface QueryOrderListRequest extends BaseRequest {
  /** 登录客户ID */
  loginCustomerId: string
  /** 语言类型 */
  langType: LangType
  /** 订单状态 */
  orderStatus?: string
  /** 订单类型 */
  orderType?: keyof typeof OrderType
  /** 开始时间，GMT0时间戳 */
  startTime?: number
  /** 结束时间，GMT0时间戳 */
  endTime?: number
  /** 当前页，默认为1 */
  currentPage?: number
  /** 每页条数，默认为10 */
  perPageCount?: number
}

export interface OrderItem {
  /** 订单ID */
  orderId: string
  /** 订单号 */
  orderSN: string
  /** 订单名称/描述 */
  orderName: string
  /** 订单状态 */
  orderStatus: string
  /** 订单类型 */
  orderType: keyof typeof OrderType
  /** 订单金额（分） */
  orderAmount: number
  /** 货币类型 */
  currencyType: keyof typeof CurrencyType
  /** 创建时间 */
  createTime: number
  /** 支付时间 */
  payTime?: number
  /** 完成时间 */
  completeTime?: number
  /** 订单商品列表 */
  orderGoods?: {
    /** 商品名称 */
    goodsName: string
    /** 商品数量 */
    quantity: number
    /** 商品价格（分） */
    price: number
  }[]
}

export interface QueryOrderListResponse {
  /** 每页条数 */
  perPageCount: number
  /** 当前页 */
  currentPage: number
  /** 总条数 */
  totalCount: number
  /** 总页数 */
  totalPageCount: number
  /** 订单列表 */
  dataList: OrderItem[]
}

// ==================== API函数类型定义 ====================

export type CreateOrderAPI = (
  params: CreateOrderRequest,
) => Promise<BaseResponse<CreateOrderResponse>>
export type DoPayByBalanceAPI = (params: DoPayByBalanceRequest) => Promise<BaseResponse<string>>
export type QueryOrderListAPI = (
  params: QueryOrderListRequest,
) => Promise<BaseResponse<QueryOrderListResponse>>
