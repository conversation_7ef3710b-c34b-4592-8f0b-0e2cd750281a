// ESimStore API 统一导出文件
// ==================== API函数导出 ====================

// 用户相关API函数
export {
  queryUserInfo,
  userLogout,
  updatePassword,
  queryBalanceInfo,
  balanceTransactionDetail,
} from './User'

// 登录注册相关API函数
export { registerUser, quickUserLogin, getVerificationCodeBySMS } from './LoginRegistration'

// 产品相关API函数
export { queryOfferList } from './Product'

// 订单相关API函数
export { createOrder, doPayByBalance } from './Order'

// 设备相关API函数
export {
  queryDeviceList,
  queryDeviceDetail,
  queryDevicePackages,
  activateDevice,
  renameDevice,
  deleteDevice,
} from './Device'

// 国家地区相关API函数
export {
  queryCountryList,
  queryCountryDetail,
  queryRegionList,
  searchCountryRegion,
  getPopularDestinations,
} from './Country'

// 支付相关API函数
export {
  createPayment,
  queryPaymentStatus,
  queryPaymentMethods,
  cancelPayment,
  applyRefund,
  queryRefundStatus,
} from './Payment'

// ==================== 常用枚举导出 ====================
export {
  DefineCode,
  LangType,
  DestinationKeyword,
  CurrencyType,
  PayMethod,
  ChannelType,
  RegisterType,
  Gender,
  IdentificationType,
  OrderType,
  MainCardType,
  PeriodUnit,
  OperateType,
  MainType,
  SimCardType,
  AdFlag,
  Consecutive,
  PayAgreeFlag,
  AreaFlag,
  MccFlag,
  GoodsType,
  CategoryCode,
} from './enums'
