// 请求参数类型
export enum DefineCode {
  STREAM = 'ESIMWEB',
  PARTNER = 'UKWEB',
}

// 语言类型枚举
export enum LangType {
  ZH_CN = 'zh-CN',
  ZH_TW = 'zh-TW',
  EN_US = 'en-US',
}

// 热门关键字类型
export enum DestinationKeyword {
  HOT_KEYWORD = 'HOT_KEYWORD',
  HOT_ESIM_KEYWORD = 'HOT_ESIM_KEYWORD',
}

// 货币类型枚举
export enum CurrencyType {
  EUR = 'EUR',
  CNY = 'CNY',
  USD = 'USD',
}

// 支付方式枚举
export enum PayMethod {
  CASH = 'CASH',
  PAYPAL = 'PAYPAL',
  UNIONPAY = 'UNIONPAY',
  ALIPAY = 'ALIPAY',
  WEIXIN = 'WEIXIN',
  ACCOUNT_AMOUNT = 'ACCOUNT_AMOUNT',
  INTEGRAL = 'INTEGRAL',
  POST_CASH = 'POST_CASH',
  POS = 'POS',
}

// 渠道类型枚举
export enum ChannelType {
  WEB = 'WEB',
  WAP = 'WAP',
  APP = 'APP',
  ORGCODE = 'ORGCODE',
  CNROAMINGWEB = 'CNROAMINGWEB',
  BTMALL = 'BTMALL',
  TMLAPP = 'TMLAPP',
}

// 注册类型枚举
export enum RegisterType {
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  COMMON = 'COMMON',
}

// 性别枚举
export enum Gender {
  MAN = 'MAN',
  WOMAN = 'WOMAN',
}

// 证件类型枚举
export enum IdentificationType {
  IDENTITY = 'IDENTITY',
  PASSPORT = 'PASSPORT',
  OTHER = 'OTHER',
}

// 订单类型枚举
export enum OrderType {
  TOPUP = 'TOPUP',
  BUYDEVICE = 'BUYDEVICE',
}

// 主卡类型枚举
export enum MainCardType {
  OTA = 1,
  OTA_ESIM = 2,
  OTA_ESIM_BLESIM = 3,
}

// 周期单位枚举
export enum PeriodUnit {
  HOUR = 0,
  DAY = 1,
  MONTH = 2,
  YEAR = 3,
  DYNAMIC_MONTH = 12,
}

// 操作类型枚举（余额流水）
export enum OperateType {
  PROXY_RECHARGE = 'PROXY_RECHARGE',
  BATCH_PROXYRECHARGE = 'BATCH_PROXYRECHARGE',
  DIRECT_RECHARGE = 'DIRECT_RECHARGE',
  TOPUP_RECHARGE = 'TOPUP_RECHARGE',
  REWARD = 'REWARD',
  COMPENSATION = 'COMPENSATION',
  REVERSE_PACKAGE = 'REVERSE_PACKAGE',
  ORDER = 'ORDER',
  DEVICE_CONTRACT_ACTIVE = 'DEVICE_CONTRACT_ACTIVE',
  TOPUP_SALESPRODUCT = 'TOPUP_SALESPRODUCT',
}

// 处理类型枚举
export enum MainType {
  SUBSTRACT = 'SUBSTRACT',
  ADD = 'ADD',
}

// 软卡类型枚举
export enum SimCardType {
  PURE_HARD_CARD = '0',
  PURE_CHIP_CARD = '1',
  CHIP_CARD_PRIORITY = '2',
  HARD_CARD_PRIORITY = '3',
}

// 广告标识枚举
export enum AdFlag {
  NO_AD = '0',
  ALIPAY_AD = '1',
}

// 连续标识枚举
export enum Consecutive {
  ON = 'ON',
  OFF = 'OFF',
}

// 支付签约标识枚举
export enum PayAgreeFlag {
  NOT_SUPPORT = '0',
  SUPPORT = '1',
}

// 流量区域标签枚举
export enum AreaFlag {
  LOCAL = 'LOCAL',
  ROAMING = 'ROAMING',
  ALL = 'ALL',
}

// 国家标识枚举
export enum MccFlag {
  WHITE = 'WHITE',
  BLACK = 'BLACK',
}

// 销售品类型枚举
export enum GoodsType {
  DISC = 'DISC',
}

// 类目编码枚举
export enum CategoryCode {
  CXTC = 'CXTC', // 畅想套餐
  LLTC = 'LLTC', // 流量套餐
}
