import type { Language } from 'element-plus/es/locale'

import dayjs from 'dayjs'
import enLocale from 'element-plus/es/locale/lang/en'
import zhCNLocale from 'element-plus/es/locale/lang/zh-cn'

import { LOCALE } from './const'
import { shallowRef } from 'vue'
const elementLocale = shallowRef<Language>(enLocale)
/**
 * 加载dayjs的语言包
 * @param lang
 */
async function loadDayjsLocale(lang) {
  let locale
  switch (lang) {
    case LOCALE.en: {
      locale = await import('dayjs/locale/en')
      break
    }
    case LOCALE.zhCn: {
      locale = await import('dayjs/locale/zh-cn')
      break
    }
    // 默认使用英语
    default: {
      locale = await import('dayjs/locale/en')
    }
  }
  if (locale) {
    dayjs.locale(locale)
  }
}

/**
 * 加载element-plus的语言包
 * @param lang
 */
async function loadElementLocale(lang) {
  switch (lang) {
    case LOCALE.en: {
      elementLocale.value = enLocale
      break
    }
    case LOCALE.zhCn: {
      elementLocale.value = zhCNLocale
      break
    }
    // 默认使用英语
    default: {
      elementLocale.value = enLocale
    }
  }
}

async function loadMessages(lang) {
  await Promise.all([loadElementLocale(lang), loadDayjsLocale(lang)])
}

export { loadMessages, elementLocale }
