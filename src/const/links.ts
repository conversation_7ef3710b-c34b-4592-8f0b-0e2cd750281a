// 社交媒体链接数据
export const LINKS_SOCIAL = [
  { name: 'Facebook', icon: 'facebook', url: 'https://www.facebook.com/GlocalMe/' },
  { name: 'X', icon: 'x', url: 'https://x.com/GlocalMeMoments' },
  { name: 'Instagram', icon: 'instagram', url: 'https://www.instagram.com/glocalmemoments' },
  { name: 'YouTube', icon: 'youtube', url: 'https://www.youtube.com/glocalme' },
  { name: 'LinkedIn', icon: 'linkedin', url: 'https://www.linkedin.com/showcase/glocalmemoments/' },
]

// 页脚链接数据
export const LINKS_FOOTER = {
  stayConnected: [
    { name: 'footer.shop_plans', icon: 'svg-icon:shop_plan', url: '#/shop-plans' },
    { name: 'footer.refer_friend', icon: 'svg-icon:refer', url: '#/refer-earn' },
    { name: 'footer.my_promos', icon: 'svg-icon:promo', url: '#/my-promos' },
  ],
  aboutUs: [
    {
      name: 'footer.about_glocalme',
      icon: 'svg-icon:about_glocalme',
      url: 'https://mph5.ucloudlink.com/appweb/glocalme/about/about_glocalme_en.html',
    },
    {
      name: 'footer.terms',
      icon: 'svg-icon:terms',
      url: 'https://mph5.ucloudlink.com/appweb/glocalme/app/agreement/privacy_policy_en.html',
    },
    {
      name: 'footer.privacy_policy',
      icon: 'svg-icon:privacy_policy',
      url: 'https://mph5.ucloudlink.com/appweb/glocalme/app/agreement/privacy_policy_en.html',
    },
    {
      name: 'footer.contact_us',
      icon: 'svg-icon:contact_us',
      url: 'https://mph5.ucloudlink.com/appweb/glocalme/about/contact_us_en.html',
    },
  ],
}

// 下载链接
export const LINKS_DOWNLOAD = [
  {
    name: 'home.download_app.app_store',
    icon: 'download_apple',
    url: 'https://apps.apple.com/app/id825314001',
  },
  {
    name: 'home.download_app.google_play',
    icon: 'download_google',
    url: 'https://play.google.com/store/apps/details/GlocalMe?id=com.wws.glocalme',
  },
]

export const LINKS_USERS = [
  { name: 'accountSettings.orders', icon: 'svg-icon:order', path: '/orders' },
  {
    name: 'accountSettings.myPromos',
    icon: 'svg-icon:promo',
    path: '/my-promos',
  },
  {
    name: 'accountSettings.pointsRewards',
    icon: 'svg-icon:points',
    path: '/points-rewards',
  },
  {
    name: 'accountSettings.title',
    icon: 'svg-icon:setting',
    path: '/account-settings',
  },
]
