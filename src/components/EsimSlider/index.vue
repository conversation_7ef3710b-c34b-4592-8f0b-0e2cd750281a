<script lang="ts" setup>
import { computed } from 'vue'
import { ref } from 'vue'

const props = defineProps({
  list: {
    type: Array<{ label: string, value: string }>,
    required: true,
  },
  size: {
    type: String,
    default: '',
  },
})

const EVENT_CHANGE = 'change'
const emit = defineEmits([EVENT_CHANGE])

const active = ref(0)
const handleSlider = function (item, index) {
  if (active.value !== index) {
    emit(EVENT_CHANGE, item)
  }
  active.value = index
}
const _getMaskStyle = computed(() => {
  return {
    width: `${100 / props.list.length}%`,
    transform: `translateX(${active.value * 100}%)`,
  }
})
const _getSliderCls = function (index) {
  return {
    'slider-item': true,
    active: active.value === index,
  }
}
</script>

<template>
  <div :class="['slider-button', size]">
    <div class="slider-button__wrapper">
      <span class="slider-mask" :style="_getMaskStyle"></span>

      <span v-for="(item, index) in list" :key="index" :class="_getSliderCls(index)" @click="handleSlider(item, index)">
        <slot :item="item" :index="index">{{ item.label }}</slot>
      </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.slider-button {
  width: 100%;
  height: 80px;
  padding: 4px;
  background: #ffffff;
  color: #000000;
  border-radius: 20px;
  border: 1px solid #e0e0e0;

  &.mobile {
    height: 56px;
    border-radius: 12px;
    border: none;

    .slider-item {
      line-height: 48px;
      font-size: 16px;
    }

    .slider-mask {
      border-radius: 8px;
    }
  }

  .slider-item {
    cursor: pointer;
    flex: 1;
    min-width: 0;
    height: 100%;
    line-height: 70px;
    font-size: 24px;
    text-align: center;
    z-index: 2;

    &.active {
      color: #ffffff;
    }
  }

  &__wrapper {
    display: flex;
    position: relative;

    .slider-mask {
      width: 50%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1;
      background: #222222;
      border-radius: 16px;
      transition: transform 0.3s ease;
    }
  }
}
</style>
