<script lang="ts" setup>
import { ref, computed } from 'vue'
import i18n from '../../plugins/i18n'
import { Icon } from '@/components/Icon/index'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  size: {
    type: String,
    default: 'medium',
    validate: (val) => ['mobile', 'small', 'medium', 'large'].includes(val),
  },
  placeholder: {
    type: String,
    default: i18n.global.t('search.placeholder'),
  },
  theme: {
    type: String,
    default: '',
  },
  align: {
    type: String,
    default: 'left',
  },
})

const EVENT_MODEL = 'update:modelValue'
const emit = defineEmits([EVENT_MODEL])

const inputVal = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit(EVENT_MODEL, val)
  },
})

const isFocus = ref(false)
const onHandleFocus = function () {
  isFocus.value = true
}
const onHandleBlur = function () {
  if (inputVal.value) return
  isFocus.value = false
}

const _getIconSize = computed(() => {
  return (
    {
      mobile: 20,
      small: 24,
      medium: 28,
      large: 36,
    }[props.size] || 28
  )
})
</script>

<template>
  <div :class="['search-box', size, theme, isFocus ? '' : align]">
    <span class="search-icon">
      <Icon icon="svg-icon:search" :size="_getIconSize"></Icon>
    </span>
    <input
      type="text"
      v-model="inputVal"
      @blur="onHandleBlur"
      @focus="onHandleFocus"
      :placeholder="placeholder"
      class="search-input"
    />
  </div>
</template>

<style lang="scss" scoped>
.search-box {
  position: relative;
  width: 100%;
  height: 58px;

  &.large {
    height: 80px;

    .search-icon {
      width: 36px;
      height: 36px;
    }

    .search-input {
      font-size: 32px;
      padding-left: 60px;
    }

    &.center .search-icon {
      margin-left: -290px;
    }
  }

  &.small {
    height: 56px;

    .search-icon {
      width: 24px;
      height: 24px;
    }

    .search-input {
      font-size: 20px;
    }

    &.center .search-icon {
      margin-left: -186px;
    }
  }

  &.mobile {
    height: 56px;

    .search-icon,
    .el-icon,
    .el-icon:deep(svg) {
      width: 24px;
      height: 24px;
    }

    .search-input {
      font-size: 14px;
      padding-left: 50px;
      border: none;
    }

    &.center {
      .search-input {
        padding-left: 16px;
      }

      .search-icon {
        margin-left: -150px;
      }
    }
  }

  &.center {
    .search-icon {
      left: 50%;
      margin-left: -220px;
    }

    .search-input {
      text-align: center;
    }
  }

  &.gray {
    .search-input {
      background-color: var(--vt-c-white-soft);
      border-color: #e0e1e0;
    }
  }
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  color: #ffffff;
}

.search-input {
  width: 100%;
  height: 100%;
  padding: 0 16px 0 50px;
  border-radius: 36px;
  border: 1px solid #d9d9d9;
  outline: none;
  font-size: 24px;

  &:focus {
    border-color: var(--base-color);
    box-shadow: 0 2px 12px rgba(0, 198, 94, 0.15);
  }

  &::placeholder {
    color: #9e9e9e;
  }
}
</style>
