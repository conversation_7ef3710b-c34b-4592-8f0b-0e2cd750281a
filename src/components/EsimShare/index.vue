<script lang="ts" setup>
defineProps({
  mode: {
    type: String,
    default: 'fill',
  },
  size: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <button :class="['esim-button', mode, size]">
    <div class="esim-button__content">
      <slot> </slot>
      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M5 12H19M19 12L12 5M19 12L12 19"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
  </button>
</template>

<style lang="scss" scoped>
@use 'sass:color';

.esim-button {
  display: inline-block;
  border: none;
  border-radius: 30px;
  padding: 10px 15px;
  font-size: 20px;
  font-weight: 500;
  line-height: 1;
  background-color: #ffffff;
  color: #000;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &.medium {
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 100px;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  &.small {
    font-size: 14px;
    padding: 8px;
    border-radius: 20px;

    .esim-button__content {
      gap: 4px;
    }

    svg {
      width: 14px;
      height: 14px;
    }
  }

  &.fill {
    background-color: var(--base-color);
    color: white;

    svg {
      color: var(--base-color);
      background: #fff;
    }

    &:hover {
      background-color: color.adjust(#4dd65d, $lightness: 5%);
    }
  }

  &__content {
    gap: 8px;
    display: flex;
    align-items: center;
  }

  svg {
    width: 20px;
    height: 20px;
    color: #ffffff;
    background: var(--base-color);
    transform: rotate(-45deg);
    border-radius: 50%;
  }

  &:hover {
    color: #ffffff;
    background-color: color.adjust(#4dd65d, $lightness: 5%);
  }
}
</style>
