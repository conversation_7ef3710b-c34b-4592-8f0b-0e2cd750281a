<script lang="ts" setup>
import type { IconTypes } from '@/components/Icon'
import { computed } from 'vue'

defineOptions({
  name: 'Icon',
})

const props = defineProps<IconTypes>()

const prefixCls = 'svg-icon'
const symbolId = computed(() => {
  return `#icon-${props.icon?.split(`${prefixCls}:`)[1]}`
})

const getIconifyStyle = computed(() => {
  const { color, size } = props
  return {
    fontSize: `${size}px`,
    '--color': color,
  }
})
</script>

<template>
  <i :class="['esim-icon', prefixCls]" :style="getIconifyStyle">
    <svg aria-hidden="true">
      <use :xlink:href="symbolId" />
    </svg>
  </i>
</template>

<style lang="scss" scoped>
.esim-icon {
  --color: inherit;
  color: var(--color);
  fill: currentColor;
  font-size: inherit;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1em;
  position: relative;
  width: 1em;
  height: 1em;
  vertical-align: middle;

  svg {
    width: 1em;
    height: 1em;
  }
}
</style>
