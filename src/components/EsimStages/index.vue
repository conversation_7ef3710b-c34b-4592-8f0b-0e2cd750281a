<script lang="ts" setup>
import { ref } from 'vue'
import { stages } from '@/data/destinations'

const props = defineProps({
  size: {
    type: String,
    default: '',
  },
  border: {
    type: Boolean,
    default: true,
  },
})
const EVENT_CHANGE = 'change'
const emits = defineEmits([EVENT_CHANGE])

const active = ref(0)
const handleStage = function (index) {
  if (active.value !== index) {
    emits(EVENT_CHANGE, index)
  }
  active.value = index
}
const _getActiveButtonCls = function (index) {
  return {
    'cricle-button': true,
    border: props.border,
    active: active.value === index,
  }
}
</script>

<template>
  <ul :class="['stage', size]">
    <li
      v-for="(item, index) in stages"
      :key="index"
      :class="_getActiveButtonCls(index)"
      @click="handleStage(index)"
    >
      <span>{{ item }}</span>
    </li>
  </ul>
</template>

<style lang="scss" scoped>
.stage {
  display: flex;

  &.mobile {
    .cricle-button {
      width: 62px;
      height: 32px;
      line-height: 32px;
      margin-left: 8px;
    }
  }
}

.cricle-button {
  cursor: pointer;
  text-align: center;
  width: 110px;
  height: 40px;
  line-height: 40px;
  background: #ffffff;
  border-radius: 20px;
  border: 1px solid #ffffff;

  &.border {
    border-color: #e0e0e0;
  }

  &.active,
  &:hover {
    color: var(--base-color);
    border-color: var(--base-color);
  }

  & + & {
    margin-left: 24px;
  }
}
</style>
