<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
import { LINKS_SOCIAL as socialLinks, LINKS_FOOTER as footerLinks } from '@/const/links'
import { getImageUrl } from '@/utils/getImageUrl'
</script>

<template>
  <section class="footer">
    <!-- 主标题 -->
    <h2 class="footer-title">{{ t('footer.start_travel') }}</h2>

    <!-- 页脚链接区域 -->
    <div class="footer-links">
      <!-- 社交媒体区域 -->
      <div class="footer-column full">
        <h3 class="column-title">{{ t('footer.follow_us') }}</h3>
        <div class="social-icons">
          <a
            v-for="social in socialLinks"
            :key="social.name"
            :href="social.url"
            :title="social.name"
            class="social-icon"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img :src="getImageUrl(`${social.icon}.svg`, 'svgs')" :alt="social.name" />
          </a>
        </div>
      </div>

      <!-- 保持联系区域 -->
      <div class="footer-column">
        <h3 class="column-title">{{ t('footer.stay_connected') }}</h3>
        <ul class="social-links">
          <li v-for="link in footerLinks.stayConnected" :key="link.name">
            <a :href="link.url">{{ t(link.name) }}</a>
          </li>
        </ul>
      </div>

      <!-- 关于我们区域 -->
      <div class="footer-column">
        <h3 class="column-title">{{ t('footer.about_us') }}</h3>
        <ul class="social-links">
          <li v-for="link in footerLinks.aboutUs" :key="link.name">
            <a :href="link.url">{{ t(link.name) }}</a>
          </li>
        </ul>
      </div>
    </div>
  </section>
</template>

<style src="./Footer.scss" lang="scss" scoped></style>
