<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Icon } from '@/components/Icon'
import { LINKS_FOOTER as footerLinks, LINKS_USERS as userLinks } from '@/const/links'
import { computed } from 'vue'

const router = useRouter()
const { t } = useI18n()

const handleSearch = () => {
  console.log('ddd ==> search')
}

const handleClose = () => {
  console.log('ddd ==> close')
}

const handleGoIndex = () => {
  router.push('/')
}

const menuIcon = ref('tool_more')
const getMenuIcon = computed(() => {
  return 'svg-icon:' + menuIcon.value
})
const onSetMenuIcon = (icon) => {
  menuIcon.value = icon
}

const menuRef = ref()
const menuLinks = ref([
  { name: 'nav.shop_plans', icon: 'svg-icon:shop_plan', path: '/shop-plans' },
  { name: 'nav.my_esims', icon: 'svg-icon:my_esim', path: '/my-esims' },
  { name: 'refer.title', icon: 'svg-icon:refer', path: '/refer-earn' },
])

const handleClickCell = (item) => {
  if (item.url) {
    window.location.href = item.url
  } else if (item.path) {
    router.push(item.path)
  }
  menuRef.value.close()
}
</script>
<template>
  <div class="mobile-header no-rem">
    <!-- 主要Header -->
    <div class="header-main">
      <!-- 左侧 -->
      <div class="header-left" @click="handleGoIndex">
        <img src="@/assets/images/Logo.svg" alt="" />
      </div>
      <!-- 右侧 -->
      <div class="header-right">
        <Icon class="search-icon" size="20" icon="svg-icon:tool_search" @click="handleSearch" />

        <div class="header-menu">
          <van-dropdown-menu ref="menuRef" active-color="var(--base-color)">
            <van-dropdown-item @open="onSetMenuIcon('close')" @close="onSetMenuIcon('tool_more')">
              <template #title>
                <Icon size="22" :icon="getMenuIcon" />
              </template>

              <div class="avator">
                <span class="avator-left"></span>
                <div class="avator-right">
                  <h6 class="avator-name">GlocalMe001</h6>
                  <p>
                    <span class="tag tag-vip">
                      <Icon icon="svg-icon:vip"></Icon>
                    </span>
                    <span class="tag tag-grade">U1</span>
                  </p>
                </div>
              </div>
              <van-cell-group inset>
                <van-cell
                  v-for="item in menuLinks"
                  :key="item.name"
                  is-link
                  clickable
                  @click="handleClickCell(item)"
                >
                  <template #title>
                    <Icon :icon="item.icon"></Icon>
                    <span style="margin-left: 5px">{{ t(item.name) }}</span>
                  </template>
                </van-cell>
              </van-cell-group>
              <van-cell-group inset title="Account">
                <van-cell
                  v-for="item in userLinks"
                  :key="item.name"
                  is-link
                  clickable
                  @click="handleClickCell(item)"
                >
                  <template #title>
                    <Icon :icon="item.icon"></Icon>
                    <span style="margin-left: 5px">{{ t(item.name) }}</span>
                  </template>
                </van-cell>
              </van-cell-group>
              <van-cell-group inset :title="t('nav.about_us')">
                <van-cell
                  v-for="item in footerLinks.aboutUs"
                  :key="item.name"
                  is-link
                  @click="handleClickCell(item)"
                >
                  <template #title>
                    <Icon :icon="item.icon"></Icon>
                    <span style="margin-left: 5px">{{ t(item.name) }}</span>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-dropdown-item>
          </van-dropdown-menu>
        </div>
      </div>
    </div>
    <!-- 下载提示条 -->
    <div class="download-banner">
      <span class="download-text">Download GlocalMe App on the App Store / Google Play</span>
      <van-icon name="cross" class="close-icon" @click="handleClose" />
    </div>
  </div>
</template>

<style scoped lang="scss">
// 为页面内容添加顶部间距，避免被固定header遮挡
:global(.mobile-page-content) {
  padding-top: 116px;
}

.no-rem {
  max-width: 750px;
}

.mobile-header {
  margin: auto;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 116px;
  background: #fafafa;

  .header-left {
    img {
      height: 18px;
    }
  }
}

.download-banner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 44px;
  padding: 0 16px;
  background: rgba(0, 198, 94, 0.06);
  font-size: 12px;
  height: 44px;

  .download-text {
    flex: 1;
    text-align: left;
    font-weight: 500;
    color: var(--base-color);
  }

  .close-icon {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.5);
    cursor: pointer;
    padding: 4px;

    &:hover {
      opacity: 0.8;
    }
  }
}

.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  height: var(--header-height);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left {
  justify-content: flex-start;
}

.header-right {
  justify-content: flex-end;
  color: #000000;
}

.search-icon {
  &:hover {
    color: var(--base-color);
  }
}

.avator {
  overflow: hidden;
  padding: 0 16px;
  margin-bottom: 24px;

  &-left {
    float: left;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #cccccc url('../images/BANNER_top_bg.png') center center;
    background-size: 60%;
    background-repeat: no-repeat;
  }

  &-right {
    float: left;
    margin-left: 16px;
    padding-top: 6px;
  }

  &-name {
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .tag {
    display: inline-block;
    width: 36px;
    height: 18px;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    overflow: hidden;
    border-radius: 20px;
    margin-right: 8px;

    &.tag-vip {
      background-color: #ffd350;
      font-size: 18px;
    }

    &.tag-grade {
      background-color: #00c65e;
      color: #ffffff;
    }
  }
}

.header-menu {
  --van-dropdown-menu-shadow: none;
  --van-dropdown-menu-background: none;
  --van-dropdown-menu-height: var(--header-height);
  --van-popup-background: var(--vt-c-white-soft);
  --van-dropdown-menu-content-max-height: 100%;

  --van-cell-group-title-color: var(--primary-color);
  --van-cell-group-title-font-size: 16px;
  --van-cell-group-inset-title-padding: 24px 16px 16px;
  --van-cell-text-color: var(--primary-color);
  --van-cell-font-size: 16px;
  --van-cell-vertical-padding: 18px;
  --van-cell-horizontal-padding: 18px;

  :deep(.van-dropdown-menu__title:after) {
    content: none;
  }

  :deep(.van-dropdown-item__content) {
    bottom: 0;
    padding-top: 24px;
    padding-bottom: 32px;

    .esim-icon {
      margin-top: -2px;
    }
  }

  :deep(.van-cell-group__title) {
    font-weight: 600;
  }
}
</style>
