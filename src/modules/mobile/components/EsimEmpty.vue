<script setup lang="ts">
import { Icon } from '@/components/Icon'

defineProps({
  description: {
    type: String,
    default: 'No Records.',
  },
})
</script>

<template>
  <div class="empty-state">
    <div class="empty-icon">
      <Icon icon="svg-icon:no-list" :size="120"></Icon>
    </div>
    <slot>
      <p class="empty-description">{{ description }}</p>
    </slot>
  </div>
</template>

<style scoped lang="scss">
// 空状态样式
.empty-state {
  text-align: center;
  padding: 24px 0;
  margin: 0 auto;
}

.empty-icon {
  margin-bottom: 24px;
}

.empty-description {
  font-size: 16px;
  color: #000000;
  line-height: 1;
}
</style>
