<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { countries, getCountryName, type Country } from '@/data/countries'

interface Props {
  modelValue?: string
  placeholder?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', country: Country | null): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '',
})

const emit = defineEmits<Emits>()

const { t, locale } = useI18n()

// 选择器状态
const showPicker = ref(false)
const searchText = ref('')

// 当前选中的国家
const selectedCountry = computed(() => {
  return countries.find((country) => country.code === props.modelValue) || null
})

// 过滤后的国家列表
const filteredCountries = computed(() => {
  if (!searchText.value) {
    return countries
  }

  const search = searchText.value.toLowerCase()
  return countries.filter((country) => {
    const name = getCountryName(country, locale.value).toLowerCase()
    const code = country.code.toLowerCase()
    return name.includes(search) || code.includes(search)
  })
})

// 显示文本
const displayText = computed(() => {
  if (selectedCountry.value) {
    return getCountryName(selectedCountry.value, locale.value)
  }
  return props.placeholder || t('register.select_country_placeholder')
})

// 选择国家
const selectCountry = (country: Country) => {
  emit('update:modelValue', country.code)
  emit('change', country)
  showPicker.value = false
  searchText.value = ''
}

// 清除选择
const clearSelection = () => {
  emit('update:modelValue', '')
  emit('change', null)
}

// 打开选择器
const openPicker = () => {
  showPicker.value = true
}

// 关闭选择器
const closePicker = () => {
  showPicker.value = false
  searchText.value = ''
}
</script>

<template>
  <div class="country-selector">
    <!-- 选择器触发器 -->
    <van-field
      :model-value="displayText"
      :label="t('register.country_region')"
      readonly
      is-link
      right-icon="arrow"
      @click="openPicker"
    />

    <!-- 国家选择弹窗 -->
    <van-popup
      v-model:show="showPicker"
      position="bottom"
      :style="{ height: '70%' }"
      round
      closeable
      @close="closePicker"
    >
      <div class="country-picker">
        <!-- 标题 -->
        <div class="picker-header">
          <h3>{{ t('register.select_residence') }}</h3>
        </div>

        <!-- 搜索框 -->
        <div class="picker-search">
          <van-field
            v-model="searchText"
            :placeholder="t('common.search')"
            left-icon="search"
            clearable
          />
        </div>
        <!-- 国家列表 -->
        <div class="picker-content">
          <van-list>
            <van-cell
              v-for="country in filteredCountries"
              :key="country.code"
              :title="getCountryName(country, locale)"
              :label="country.code"
              is-link
              @click="selectCountry(country)"
            >
              <template #icon>
                <span class="country-flag">{{ country.flag }}</span>
              </template>
              <template #right-icon>
                <van-icon
                  v-if="selectedCountry?.code === country.code"
                  name="success"
                  color="var(--base-color)"
                />
              </template>
            </van-cell>
          </van-list>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style scoped lang="scss">
.country-selector {
  width: 100%;
}

.country-picker {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.picker-header {
  padding: 0.533rem 0.427rem; // 20px 16px转换为rem
  border-bottom: 1px solid #ebedf0;
  text-align: center;

  h3 {
    margin: 0;
    font-size: 0.48rem; // 18px转换为rem
    font-weight: 600;
    color: #323233;
  }
}

.picker-search {
  padding: 0.427rem; // 16px转换为rem
  border-bottom: 1px solid #ebedf0;
}

.picker-content {
  flex: 1;
  overflow-y: auto;
}

.country-flag {
  font-size: 0.533rem; // 20px转换为rem
  margin-right: 0.32rem; // 12px转换为rem
  display: inline-block;
  width: 0.64rem; // 24px转换为rem
  text-align: center;
}

:deep(.van-cell) {
  padding: 0.32rem 0.427rem; // 12px 16px转换为rem

  .van-cell__title {
    font-size: 0.427rem; // 16px转换为rem
    color: #323233;
  }

  .van-cell__label {
    font-size: 0.373rem; // 14px转换为rem
    color: #969799;
    margin-top: 0.107rem; // 4px转换为rem
  }
}

:deep(.van-field) {
  .van-field__control {
    color: #323233;
  }

  &[readonly] .van-field__control {
    color: #646566;
  }
}

:deep(.van-popup) {
  .van-popup__close-icon {
    top: 0.427rem; // 16px转换为rem
    right: 0.427rem; // 16px转换为rem
  }
}
</style>
