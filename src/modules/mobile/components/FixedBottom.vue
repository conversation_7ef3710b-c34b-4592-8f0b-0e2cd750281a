<script lang="ts" setup>
defineProps({
  text: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  align: {
    type: String,
    default: '',
  },
})

const ENENT_CLICK = 'click-button'
const emit = defineEmits([ENENT_CLICK])

const handleClick = function () {
  emit(ENENT_CLICK)
}
</script>

<template>
  <div :class="['fixed-bottom', align]">
    <slot>
      <van-button type="primary" size="large" block round :disabled="disabled" @click="handleClick">
        {{ text }}
      </van-button>
    </slot>
  </div>
</template>

<style lang="scss" scoped>
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 8px 16px;
  border-top: 1px solid #e0e0e0;
  z-index: 100;

  &.between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
