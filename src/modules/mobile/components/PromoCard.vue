<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import { Icon } from '@/components/Icon/index'
import { type PromoCode } from '@/data/promos'

interface Props {
  promo: PromoCode
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
})

const EVENT_SELECTED = 'selected'
const emit = defineEmits([EVENT_SELECTED])
const handleClick = () => {
  if (props.promo.isDisabled) return
  emit(EVENT_SELECTED, props.promo)
}

const { t } = useI18n()
</script>

<template>
  <div
    class="promo-card"
    :class="{ selected: promo.isSelected, disabled: promo.isDisabled }"
    @click="handleClick"
  >
    <!-- 最佳优惠标签 -->
    <div v-if="promo.isBestSaving" class="best-savings-tag">
      {{ t('promo.best_savings') }}
    </div>

    <div class="promo-content">
      <!-- 优惠值 -->
      <div class="promo-value">
        <template v-if="promo.type === 'percent'">
          <div class="percent-value">{{ promo.value }}%</div>
          <div class="off-text">off</div>
        </template>
        <template v-else>
          <div class="fixed-amount">{{ promo.code }}</div>
        </template>
      </div>

      <!-- 优惠码详情 -->
      <div class="promo-details">
        <div class="promo-label">
          <template v-if="promo.type === 'percent'">
            {{ t('promo.promo_code') }}: {{ promo.code }}
          </template>
          <template v-else>{{ t('promo.coupon') }}</template>
        </div>
        <div class="promo-code">{{ promo.condition }}</div>
        <div class="promo-validity">{{ t('promo.valid_until') }} {{ promo.validUntil }}</div>
      </div>

      <!-- 选择指示器 -->
      <div v-if="!promo.isDisabled && !readonly" class="selection-indicator">
        <div class="circle" :class="{ checked: promo.isSelected }">
          <Icon v-if="promo.isSelected" icon="svg-icon:complete" size="20" color="#fff"></Icon>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.promo-card {
  position: relative;
  background-color: #ffffff;
  border-radius: 16px;
  padding: 20px 16px 20px 0;
  border: 1px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  &.selected {
    border-color: var(--base-color);
  }

  &.disabled {
    opacity: 0.7;

    .promo-value,
    .promo-details,
    .promo-code {
      color: #ccc;
    }
  }

  .best-savings-tag {
    position: absolute;
    top: -1px;
    right: -1px;
    background-color: var(--base-color);
    color: white;
    font-size: 12px;
    padding: 3px 8px;
    border-top-right-radius: 12px;
    border-bottom-left-radius: 12px;
  }
}

.promo-content {
  display: flex;
  align-items: center;
}

.promo-value {
  width: 98px;
  text-align: center;
  color: #ff2424;

  .percent-value {
    font-size: 18px;
    font-weight: 700;
    line-height: 1;
  }

  .off-text {
    font-size: 14px;
    line-height: 1;
    margin-top: 8px;
  }

  .fixed-amount {
    font-size: 18px;
    font-weight: 700;
  }
}

.promo-details {
  flex: 1;
  min-width: 0;
  font-size: 14px;
  line-height: 1;
  color: rgba(0, 0, 0, 0.5);
  padding-left: 12px;
  border-left: 1px dashed #e0e0e0;

  .promo-code {
    color: #000000;
    font-weight: 600;
    margin: 12px 0;
  }
}

.selection-indicator {
  margin-left: 16px;

  .circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;

    &.checked {
      background-color: var(--base-color);
      border-color: var(--base-color);
    }
  }
}
</style>
