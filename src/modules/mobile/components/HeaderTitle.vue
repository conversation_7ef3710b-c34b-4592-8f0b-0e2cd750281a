<script lang="ts" setup>
import { useRouter } from 'vue-router'
const router = useRouter()
import { Icon } from '@/components/Icon'

defineProps({
  title: {
    type: String,
    required: true,
  },
})

const handleBack = () => {
  router.back()
}
</script>

<template>
  <div class="top-bar">
    <span class="left">
      <Icon icon="svg-icon:left-arrow" size="16" color="#222" @click="handleBack"></Icon>
    </span>
    <h1 class="title">{{ title }}</h1>
  </div>
</template>

<style lang="scss" scoped>
.top-bar {
  padding: 0 16px;
  line-height: 56px;
  position: relative;

  .title {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #000000;
  }

  .left {
    position: absolute;
    left: 16px;
    top: 0;
    height: 100%;
    z-index: 2;
  }
}
</style>
