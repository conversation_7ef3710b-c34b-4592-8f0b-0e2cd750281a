// 移动端Footer样式将在这里添加
.footer {
  margin-top: 30px;
  padding-top: 30px;
}

.footer-title {
  font-size: 18px;
  line-height: 1;
  color: #000000;
  text-align: center;
  text-transform: uppercase;
  font-weight: bold;
}

.footer-links {
  padding: 0 16px;
  margin-top: 48px;
  overflow: hidden;

  .footer-column {
    float: left;
    width: 50%;
    margin-bottom: 36px;
    &.full {
      width: 100%;
      margin-bottom: 60px;
    }
  }

  .column-title {
    color: #000000;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 24px;
  }

  .social-icons {
    overflow: hidden;
    .social-icon {
      float: left;
      & + .social-icon {
        margin-left: 25px;
      }

      img {
        display: block;
        width: 24px;
        height: 24px;
      }
    }
  }

  .social-links {
    color: rgba(0, 0, 0, 0.5);
    font-size: 14px;
    padding-top: 6px;
    li + li {
      margin-top: 24px;
    }

    a {
      color: rgba(0, 0, 0, 0.5);
      padding: 0;
    }
  }
}
