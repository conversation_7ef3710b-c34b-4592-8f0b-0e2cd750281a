<script lang="ts" setup>
import EsimSearch from '@/components/EsimSearch/index.vue'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()
</script>

<template>
  <div class="top-search">
    <h1 class="title">
      {{ t('home.title') }}
      <span class="highlight">{{ t('home.title_highlight') }}</span>
    </h1>

    <EsimSearch size="mobile" placeholder="Search 200+ countries and regions..." />
  </div>
</template>

<style lang="scss" scoped>
.top-search {
  padding: 48px 16px 0;
  text-align: center;
}

.title {
  color: #000000;
  font-size: 36px;
  line-height: 48px;
  font-weight: bold;
  margin-bottom: 36px;
}

.highlight {
  color: #156326;
}
</style>
