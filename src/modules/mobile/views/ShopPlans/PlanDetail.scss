.plan-detail {
  padding-bottom: 92px;
  &-content {
    padding: 16px;
  }
}

.plans-section {
  margin-bottom: 24px;

  .section-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 12px;
    color: #333;
  }
}

.plan-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 22px 48px 22px 16px;
  position: relative;
  border: 1px solid #fff;
  transition: all 0.3s ease;
  margin-bottom: 12px;
  &:last-child {
    margin-bottom: 0;
  }

  &.selected {
    border-color: var(--base-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.plan-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.plan-data {
  font-size: 18px;
  font-weight: 700;
  color: #000;
}

.network-tag {
  background-color: #222222;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 4px;
  color: white;
}

.onsale-tag {
  background-color: #ffae00;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.plan-days {
  font-size: 12px;
  line-height: 1;
  color: #000;
  margin-top: 16px;
}

.coverage-info {
  margin-top: 16px;
  display: flex;

  .coverage-label {
    font-size: 12px;
    color: #000000;
  }

  .coverage-region {
    font-size: 12px;
    color: var(--base-color);
  }

  .region-link {
    display: flex;
    align-items: center;
    margin-left: 1px;
    word-break: break-all;
  }
}

.plan-best {
  position: absolute;
  right: -1px;
  top: -1px;
  z-index: 2;
  background-color: var(--base-color);
  color: white;
  font-size: 12px;
  padding: 4px 13px;
  border-top-right-radius: 12px;
  border-bottom-left-radius: 12px;
}

.plan-price {
  position: absolute;
  right: 48px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  text-align: center;

  .price {
    font-size: 14px;
    color: #000;
    font-weight: 700;
  }

  .original-price {
    margin-top: 10px;
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
  }
}

.plan-indicator {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;

  .circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;

    &.checked {
      border-color: var(--base-color);
      background-color: var(--base-color);
    }
  }
}
