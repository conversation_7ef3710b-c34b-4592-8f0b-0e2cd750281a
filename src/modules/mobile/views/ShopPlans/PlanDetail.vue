<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Icon } from '@/components/Icon'
import PageHeader from '@/modules/mobile/components/HeaderTitle.vue'
import FixedBottom from '@mobile/components/FixedBottom.vue'

interface Plan {
  id: number
  data: string
  days: string
  price: number
  originalPrice?: number
  best?: boolean
  onsale?: boolean
  network?: boolean
  isSelected?: boolean
}

interface RegionalPlan extends Plan {
  coverage: {
    region: string
    countries: number
  }
}

const route = useRoute()
const router = useRouter()
const { t } = useI18n()

const localPlans = ref<Plan[]>([
  {
    id: 1,
    data: '5GB',
    days: '3 Days',
    price: 11.0,
    originalPrice: 22.0,
    network: true,
    onsale: true,
    isSelected: false,
  },
  { id: 2, data: '10GB', days: '7 Days', price: 29.0, best: true, isSelected: false },
  { id: 3, data: '20GB', days: '7 Days', price: 49.0, network: true, isSelected: false },
  { id: 4, data: '50GB', days: '30 Days', price: 89.0, isSelected: false },
])

const regionalPlans = ref<RegionalPlan[]>([
  {
    id: 5,
    data: '10GB',
    days: '30 Days',
    price: 29.0,
    isSelected: false,
    coverage: {
      region: 'North America',
      countries: 3,
    },
  },
  {
    id: 6,
    data: '20GB',
    days: '30 Days',
    price: 69.0,
    isSelected: false,
    coverage: {
      region: 'Europe',
      countries: 40,
    },
  },
])

const currentPlan = computed(() => {
  const selectedLocal = localPlans.value.find((plan) => plan.isSelected)
  const selectedRegional = regionalPlans.value.find((plan) => plan.isSelected)
  return selectedLocal || selectedRegional
})

const selectPlan = (plan: Plan | RegionalPlan) => {
  // Deselect all plans
  localPlans.value.forEach((p) => (p.isSelected = false))
  regionalPlans.value.forEach((p) => (p.isSelected = false))

  // Select the clicked plan
  plan.isSelected = true
}

const countryName = computed(() => {
  return (route.query.name as string) || '--'
})

const handleNextTo = () => {
  if (currentPlan.value) {
    router.push({
      name: 'checkout',
      params: { id: currentPlan.value.id },
    })
  }
}

const handleOpenRegion = (plan: RegionalPlan) => {
  // Handle opening region details
  console.log('Open region details for:', plan.coverage.region)
}
</script>

<template>
  <div class="plan-detail">
    <PageHeader :title="countryName" />

    <div class="plan-detail-content">
      <!-- Local Plans -->
      <div class="plans-section">
        <h2 class="section-title">Local Plans</h2>
        <div class="plans-list">
          <div
            v-for="plan in localPlans"
            :key="plan.id"
            class="plan-card"
            :class="{ selected: plan.isSelected }"
            @click="selectPlan(plan)"
          >
            <div class="plan-header">
              <div class="plan-data">{{ plan.data }}</div>
              <div v-if="plan.network" class="network-tag">
                <span>5G</span>
              </div>
              <div v-if="plan.onsale" class="onsale-tag">
                <span>ON SALE</span>
              </div>
            </div>
            <div class="plan-days">{{ plan.days }}</div>

            <div class="plan-price">
              <span class="price">USD {{ plan.price.toFixed(2) }}</span>
              <p v-if="plan.originalPrice" class="original-price">
                {{ plan.originalPrice.toFixed(2) }}
              </p>
            </div>

            <div v-if="plan.best" class="plan-best">
              <span>Best Value</span>
            </div>

            <div class="plan-indicator">
              <div class="circle" :class="{ checked: plan.isSelected }">
                <Icon v-if="plan.isSelected" icon="svg-icon:complete" size="20" color="#fff"></Icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Regional Plans -->
      <div class="plans-section">
        <h2 class="section-title">Regional Plans</h2>
        <div class="plans-list">
          <div
            v-for="plan in regionalPlans"
            :key="plan.id"
            class="plan-card"
            :class="{ selected: plan.isSelected }"
            @click="selectPlan(plan)"
          >
            <div class="plan-header">
              <div class="plan-data">{{ plan.data }}</div>
            </div>
            <div class="plan-days">{{ plan.days }}</div>
            <div class="coverage-info">
              <div class="coverage-label">Coverage:</div>
              <div class="coverage-region">
                <span class="region-link" @click.stop="handleOpenRegion(plan)">
                  {{ plan.coverage.region }} ({{ plan.coverage.countries }}) ›
                </span>
              </div>
            </div>

            <div class="plan-price">
              <p class="price">USD {{ plan.price.toFixed(2) }}</p>
            </div>

            <div class="plan-indicator">
              <div class="circle" :class="{ checked: plan.isSelected }">
                <Icon v-if="plan.isSelected" icon="svg-icon:complete" size="20" color="#fff"></Icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <FixedBottom>
      <van-button
        type="primary"
        size="large"
        block
        round
        :disabled="!currentPlan"
        @click="handleNextTo"
      >
        {{ t('checkout.title') }}
      </van-button>
    </FixedBottom>
  </div>
</template>

<style lang="scss" scoped src="./PlanDetail.scss"></style>
