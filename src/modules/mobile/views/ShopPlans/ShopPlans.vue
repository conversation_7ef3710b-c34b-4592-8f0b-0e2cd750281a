<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { destinations } from '@/data/destinations'
import EsimSlider from '@/components/EsimSlider/index.vue'
import EsimStages from '@/components/EsimStages/index.vue'
import EsimDestination from '../../components/EsimDestination.vue'

const { t } = useI18n()

const router = useRouter()
const handleTo = (item) => {
  router.push({
    name: 'plan-detail',
    params: {
      pid: item.id,
    },
    query: {
      name: item.name,
    },
  })
}
const handleFilter = function () {}
</script>

<template>
  <section class="shop-plans">
    <h2 class="section-title">{{ t('home.popular.title') }}</h2>

    <EsimSlider
      size="mobile"
      :list="[t('home.popular.local'), t('home.popular.regional')]"
    ></EsimSlider>

    <div class="stages-wrapper">
      <EsimStages size="mobile" :border="false" @change="handleFilter"></EsimStages>
    </div>

    <EsimDestination :list="destinations" @click-cell="handleTo"></EsimDestination>
  </section>
</template>

<style lang="scss" scoped>
.shop-plans {
  padding: 0 16px;
  margin-top: 36px;
  margin-bottom: 46px;
}

.section-title {
  padding: 24px 0 36px;
  font-size: 24px;
  line-height: 1;
  font-weight: bold;
  text-align: center;
  color: #000000;
}

.stages-wrapper {
  margin-top: 24px;
  margin-bottom: 12px;
}
</style>
