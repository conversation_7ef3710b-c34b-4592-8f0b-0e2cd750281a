<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

defineProps({
  type: {
    type: String,
    required: true,
  },
  date: {
    type: String,
    required: true,
  },
})
</script>

<template>
  <div class="reward-item">
    <div class="reward-info">
      <h5 class="reward-type">
        {{ t(`refer.${type}_rewards`) }}
      </h5>
      <p class="reward-date">{{ date }}</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.reward-item {
  line-height: 1;
  padding: 20px 16px;
  border-radius: 12px;
  margin-bottom: 12px;
  background-color: #fff;

  &:last-child {
    margin-bottom: 0;
  }
}

.reward-type {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  margin-bottom: 12px;
}

.reward-date {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.5);
}
</style>
