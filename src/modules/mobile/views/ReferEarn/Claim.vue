<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import PageHeader from '@/modules/mobile/components/HeaderTitle.vue'
import DownloadApp from '@mobile/views/Home/DownloadApp.vue'
import Footer from '@mobile/components/Footer.vue'
import Copyright from '@mobile/components/Copyright.vue'

const { t } = useI18n()
const router = useRouter()

// 促销代码 - 复用web版的数据
const promoCode = ref('CIEL1234')

// 复制促销代码
const copyPromoCode = () => {
  navigator.clipboard
    .writeText(promoCode.value)
    .then(() => {
      showToast(t('claim.copy_success'))
    })
    .catch(() => {
      showToast(t('claim.copy_failed'))
    })
}

// 注册并领取
const signUpAndClaim = () => {
  // 这里可以实现跳转到注册页面的逻辑
  router.push({ name: 'register', query: { promo: promoCode.value } })
}
</script>

<template>
  <!-- 顶部导航 -->
  <PageHeader :title="t('claim.title')" />

  <!-- 主要内容 -->
  <div class="refer-earn">
    <!-- 图片区域 -->
    <div class="refer-image">
      <img src="../../images/refer.png" alt="Try eSIM" />
    </div>

    <!-- 标题和描述 -->
    <h2 class="refer-reward-title">
      {{ t('claim.reward_title') }}
    </h2>

    <h3 class="refer-subtitle">
      {{ t('claim.subtitle') }}
    </h3>

    <!-- 促销代码部分 -->
    <div class="referral-code-section">
      <div class="code-container">
        <p class="referral-label">{{ t('claim.code_label') }}</p>
        <span class="referral-code">{{ promoCode }}</span>
      </div>
      <van-button class="copy-btn" plain round size="small" @click="copyPromoCode">
        {{ t('claim.copy') }}
      </van-button>
    </div>

    <!-- 注册按钮 -->
    <van-button type="primary" block round size="large" @click="signUpAndClaim">
      {{ t('claim.sign_up') }}
    </van-button>

    <!-- 使用说明 -->
    <div class="claim-guide">
      <h3 class="guide-title">{{ t('claim.how_to_use') }}</h3>
      <ol class="guide-steps">
        <li>{{ t('claim.step1') }}</li>
        <li>{{ t('claim.step2') }}</li>
        <li>{{ t('claim.step3') }}</li>
      </ol>
    </div>
  </div>
  <DownloadApp />
  <Footer />
  <Copyright />
</template>

<style lang="scss" scoped src="./ReferEarn.scss"></style>

<style lang="scss" scoped>
.claim-guide {
  margin-top: 40px;
}

.guide-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}

.guide-steps {
  li {
    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.5);
  }
}
</style>
