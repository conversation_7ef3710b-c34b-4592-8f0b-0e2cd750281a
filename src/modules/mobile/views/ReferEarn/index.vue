<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { showToast } from 'vant'
import RewardItem from './components/RewardItem.vue'
import PageHeader from '@/modules/mobile/components/HeaderTitle.vue'
import DownloadApp from '@mobile/views/Home/DownloadApp.vue'
import Footer from '@mobile/components/Footer.vue'
import Copyright from '@mobile/components/Copyright.vue'

const { t } = useI18n()

// 引用代码 - 复用web版的数据
const referralCode = ref('CIEL1234')

// 复制引用代码
const copyReferralCode = () => {
  navigator.clipboard
    .writeText(referralCode.value)
    .then(() => {
      showToast(t('refer.copy_success'))
    })
    .catch(() => {
      showToast(t('refer.copy_failed'))
    })
}

// 分享功能
const shareNow = () => {
  if (navigator.share) {
    navigator
      .share({
        title: t('refer.share_title'),
        text: t('refer.share_text', { code: referralCode.value }),
        url: window.location.href,
      })
      .catch(() => {
        showToast(t('refer.share_manually'))
      })
  } else {
    copyReferralCode()
    showToast(t('refer.share_manually'))
  }
}

// 奖励历史数据
const rewardList = reactive([
  { id: 1, type: 'invitation', date: '2025/01/01' },
  { id: 2, type: 'invitation', date: '2025/05/01' },
])
</script>

<template>
  <!-- 顶部导航 -->
  <PageHeader :title="t('refer.title')" />

  <!-- 主要内容 -->
  <div class="refer-earn">
    <!-- 图片区域 -->
    <div class="refer-image">
      <img src="../../images/refer.png" alt="Refer and Earn" />
    </div>

    <!-- 标题和描述 -->
    <h2 class="refer-reward-title">
      {{ t('refer.reward_title') }}
    </h2>

    <h3 class="refer-subtitle">
      {{ t('refer.subtitle') }}
    </h3>

    <p class="refer-description">
      {{ t('refer.description') }}
    </p>

    <!-- 引用代码部分 -->
    <div class="referral-code-section">
      <div class="code-container">
        <p class="referral-label">{{ t('refer.your_code') }}</p>
        <span class="referral-code">{{ referralCode }}</span>
      </div>
      <van-button class="copy-btn" plain round size="small" @click="copyReferralCode">
        {{ t('refer.copy') }}
      </van-button>
    </div>

    <!-- 分享按钮 -->
    <van-button type="primary" block round size="large" @click="shareNow">
      {{ t('refer.share_now') }}
    </van-button>

    <!-- 奖励历史部分 -->
    <div class="reward-history-section">
      <h2 class="reward-history-title">
        {{ t('refer.reward_history') }}
      </h2>

      <div class="reward-list" v-if="rewardList.length > 0">
        <RewardItem v-for="item in rewardList" :key="item.id" :type="item.type" :date="item.date" />
      </div>
    </div>
  </div>

  <DownloadApp />
  <Footer />
  <Copyright />
</template>

<style lang="scss" scoped src="./ReferEarn.scss"></style>
