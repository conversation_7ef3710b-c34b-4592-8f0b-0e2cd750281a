.refer-earn {
  padding: 0 16px 60px;
  display: flex;
  flex-direction: column;
}

.refer-image {
  max-width: 242px;
  margin: 15px auto;
  border-radius: 8px;
  overflow: hidden;

  img {
    width: 100%;
    display: block;
  }
}

.refer-reward-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--base-color);
  margin: 24px 0 12px;
  text-align: left;
}

.refer-subtitle {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #000000;
}

.refer-description {
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 24px;
}

.referral-code-section {
  background-color: #ffffff;
  border-radius: 20px;
  padding: 24px 16px;
  line-height: 1;
  margin-bottom: 24px;
  position: relative;
}

.referral-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 12px;
}

.code-copy-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.referral-code {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
}

.copy-btn {
  --van-button-default-border-color: #000;
  --van-button-small-padding: 0 20px;
  --van-button-small-height: 36px;

  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.reward-history-section {
  margin-top: 40px;
}

.reward-history-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 12px;
}
