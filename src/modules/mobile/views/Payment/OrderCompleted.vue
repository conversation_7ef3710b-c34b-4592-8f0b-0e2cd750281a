<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const router = useRouter()

// 模拟订单数据
const orderNumber = ref('202503291221070328855560')

// 导航到订单列表
const goToOrders = () => {
  router.push('/orders')
}

// 查看eSIM详情
const viewEsimDetails = () => {
  router.push({ name: 'esim-detail', params: { id: orderNumber.value } })
}
</script>

<template>
  <div class="order-completed">
    <div class="order-completed-content">
      <!-- 成功图标 -->
      <div class="success-icon">
        <div class="icon-circle">
          <Icon icon="svg-icon:complete" :size="48" color="#fff" />
        </div>
      </div>

      <!-- 成功标题 -->
      <h1 class="success-title">{{ t('order.completed_title') }}</h1>

      <!-- 安装指南 -->
      <div class="install-guide">
        <h2 class="guide-title">{{ t('order.how_to_install') }}</h2>
        <p class="guide-text">{{ t('order.install_instruction') }}</p>

        <van-button
          class="esim-details-button"
          type="primary"
          size="large"
          block
          round
          @click="viewEsimDetails"
        >
          <span class="button-icon">
            <Icon icon="svg-icon:esim_stroke" :size="20" color="#fff" />
          </span>
          <span class="button-text">{{ t('order.esim_details') }}</span>
          <span class="button-arrow">
            <Icon icon="svg-icon:arrow_right" :size="16" color="#fff" />
          </span>
        </van-button>

        <!-- <button class="esim-details-button" @click="">
            <span class="button-icon">
              <Icon icon="svg-icon:esim_stroke" :size="20" color="#fff" />
            </span>
            <span class="button-text">{{ t('order.esim_details') }}</span>
            <span class="button-arrow">
              <Icon icon="svg-icon:arrow_right" :size="16" color="#fff" />
            </span>
          </button> -->

        <!-- 订单链接 -->
        <a class="orders-link" @click="goToOrders">
          {{ t('order.go_to_orders') }}
        </a>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import './OrderCompleted.scss';
</style>
