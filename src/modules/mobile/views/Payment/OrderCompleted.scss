.order-completed {
  padding: 60px 16px;
}

.order-completed-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.success-icon {
  margin-bottom: 24px;

  .icon-circle {
    width: 48px;
    height: 48px;
    background-color: #00c853;
    border-radius: 50%;
  }
}

.success-title {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 48px;
}

.install-guide {
  width: 100%;
  text-align: left;
}

.guide-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 16px;
}

.guide-text {
  font-size: 14px;
  line-height: 1;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 24px;
}

.esim-details-button {
  --van-button-round-radius: 16px;
  margin-bottom: 48px;
  padding: 0 19px;
  text-align: left;

  :deep(.van-button__content) {
    width: 100%;
    justify-self: start;
  }
  :deep(.van-button__text) {
    width: 100%;
    display: flex;
  }

  .button-icon {
    margin-right: 16px;
  }

  .button-text {
    flex: 1;
    text-align: left;
    font-size: 16px;
    font-weight: 500;
  }

  .button-arrow {
    margin-left: auto;
  }
}

.orders-link {
  font-size: 14px;
  color: var(--base-color);
  text-decoration: none;
  position: relative;
  cursor: pointer;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--base-color);
  }

  &:active {
    opacity: 0.7;
  }
}
