<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const router = useRouter()

// 返回结账页面
const backToCheckout = () => {
  router.back()
}
</script>

<template>
  <div class="payment-failed">
    <div class="payment-failed-content">
      <!-- 错误图标 -->
      <div class="error-icon">
        <div class="icon-circle">
          <Icon icon="svg-icon:failed" :size="48" color="#fff" />
        </div>
      </div>

      <!-- 错误标题 -->
      <h1 class="error-title">{{ t('payment.failed_title') }}</h1>

      <!-- 错误信息 -->
      <p class="error-message">{{ t('payment.error_message') }}</p>
      <p class="error-description">
        {{ t('payment.error_description') }}
      </p>

      <!-- 返回按钮 -->
      <div class="error-btn">
        <van-button type="primary" size="large" block round @click="backToCheckout">
          {{ t('payment.back_to_checkout') }}
        </van-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import './OrderFailed.scss';
</style>
