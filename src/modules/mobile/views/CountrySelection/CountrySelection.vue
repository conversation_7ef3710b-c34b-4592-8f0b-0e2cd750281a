<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuth } from '@/hooks/useAuth'
import { countries, getCountryName, type Country } from '@/data/countries'
import { showToast, showLoadingToast, closeToast } from 'vant'

const { t, locale } = useI18n()
const router = useRouter()
const {
  email,
  password,
  selectedCountry,
  isLoading,
  canSubmitRegister,
  register,
  setSelectedCountry,
  resetForm,
} = useAuth()

// 搜索文本
const searchText = ref('')

// 过滤后的国家列表
const filteredCountries = computed(() => {
  if (!searchText.value) {
    return countries
  }

  const search = searchText.value.toLowerCase()
  return countries.filter((country) => {
    const name = getCountryName(country, locale.value).toLowerCase()
    const code = country.code.toLowerCase()
    return name.includes(search) || code.includes(search)
  })
})

// 当前选中的国家对象
const selectedCountryObj = computed(() => {
  return countries.find((country) => country.code === selectedCountry.value) || null
})

// 选择国家
const selectCountry = (country: Country) => {
  setSelectedCountry(country.code)
}

// 处理注册
const handleRegister = async () => {
  if (!canSubmitRegister.value) return

  showLoadingToast({
    message: t('register.registering'),
    forbidClick: true,
  })

  const result = await register(email.value, password.value, selectedCountry.value)
  closeToast()

  if (result.success) {
    showToast(result.message || t('register.register_success'))
    resetForm()
    router.push('/')
  } else {
    showToast(result.message || t('register.register_failed'))
    if (result.shouldRedirectToLogin) {
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  }
}

// 返回注册页面
const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="country-selection-container">
    <div class="country-selection-content">
      <!-- 搜索框 -->
      <div class="search-section">
        <van-field
          v-model="searchText"
          :placeholder="t('common.search')"
          left-icon="search"
          clearable
        />
      </div>

      <!-- 国家列表 -->
      <div class="countries-list">
        <van-list>
          <van-cell
            v-for="country in filteredCountries"
            :key="country.code"
            :title="getCountryName(country, locale)"
            :label="country.code"
            is-link
            @click="selectCountry(country)"
          >
            <template #icon>
              <span class="country-flag">{{ country.flag }}</span>
            </template>
            <template #right-icon>
              <van-icon
                v-if="selectedCountry === country.code"
                name="success"
                color="var(--base-color)"
              />
            </template>
          </van-cell>
        </van-list>
      </div>

      <!-- 底部注册按钮 -->
      <div class="register-button-section">
        <div v-if="selectedCountryObj" class="selected-country-info">
          <span class="country-flag">{{ selectedCountryObj.flag }}</span>
          <span class="country-name">{{ getCountryName(selectedCountryObj, locale) }}</span>
        </div>

        <van-button
          type="primary"
          size="large"
          block
          round
          class="register-btn"
          :loading="isLoading"
          :disabled="!canSubmitRegister"
          @click="handleRegister"
        >
          {{ t('register.sign_up') }}
        </van-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.country-selection-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.country-selection-header {
  background: white;
  border-bottom: 1px solid #ebedf0;
}

.country-selection-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0.427rem; // 16px转换为rem
}

.search-section {
  background: white;
  border-radius: 0.213rem; // 8px转换为rem
  margin-bottom: 0.427rem; // 16px转换为rem
  padding: 0.213rem; // 8px转换为rem
}

.countries-list {
  flex: 1;
  background: white;
  border-radius: 0.213rem; // 8px转换为rem
  overflow: hidden;
  margin-bottom: 0.427rem; // 16px转换为rem
}

.country-flag {
  font-size: 0.533rem; // 20px转换为rem
  margin-right: 0.32rem; // 12px转换为rem
  display: inline-block;
  width: 0.64rem; // 24px转换为rem
  text-align: center;
}

.register-button-section {
  background: white;
  border-radius: 0.213rem; // 8px转换为rem
  padding: 0.427rem; // 16px转换为rem
}

.selected-country-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.427rem; // 16px转换为rem
  padding: 0.32rem; // 12px转换为rem
  background: #f7f8fa;
  border-radius: 0.213rem; // 8px转换为rem

  .country-name {
    font-size: 0.427rem; // 16px转换为rem
    font-weight: 500;
    color: #323233;
  }
}

.register-btn {
  height: 1.493rem; // 56px转换为rem
  font-size: 0.427rem; // 16px转换为rem
  font-weight: 600;
}

:deep(.van-cell) {
  padding: 0.32rem 0.427rem; // 12px 16px转换为rem

  .van-cell__title {
    font-size: 0.427rem; // 16px转换为rem
    color: #323233;
  }

  .van-cell__label {
    font-size: 0.373rem; // 14px转换为rem
    color: #969799;
    margin-top: 0.107rem; // 4px转换为rem
  }
}

:deep(.van-field) {
  .van-field__control {
    color: #323233;
    font-size: 0.427rem; // 16px转换为rem
  }
}
</style>
