// 移动端ReferAFriend样式将在这里添加
.refer-a-friend {
  padding: 0 16px;
  margin-top: 12px;
  margin-bottom: 36px;
}

.refer-inner {
  display: flex;
  padding: 0 12px;
  border-radius: 12px;
  background: url('../../images/BANNER_share_bg.png') center 100% no-repeat;
  background-size: cover;
}

.refer-img,
.refer-content {
  flex: 1;
  min-width: 0;
}

.refer-img {
  position: relative;
  img {
    max-width: 100%;
    position: absolute;
    bottom: 0;
  }
}

.refer-content {
  padding-top: 14px;
  padding-bottom: 14px;
}

.refer-txt {
  font-size: 16px;
  line-height: 22px;
  color: #000000;
  margin-bottom: 12px;

  &__highlight {
    font-weight: bold;
  }
}
