<script setup lang="ts">
import { Swipe as <PERSON><PERSON><PERSON><PERSON>, SwipeItem as VanSwipeItem } from 'vant'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// 工作流程步骤数据
const workflowSteps = [
  {
    id: 1,
    image: '/src/assets/images/how-it-works/phone-login.png',
    step: t('home.how_it_works.steps.step1.step'),
    title: t('home.how_it_works.steps.step1.title'),
    description: t('home.how_it_works.steps.step1.description'),
  },
  {
    id: 2,
    image: '/src/assets/images/how-it-works/phone-shop.png',
    step: t('home.how_it_works.steps.step2.step'),
    title: t('home.how_it_works.steps.step2.title'),
    description: t('home.how_it_works.steps.step2.description'),
  },
  {
    id: 3,
    image: '/src/assets/images/how-it-works/phone-activation.png',
    step: t('home.how_it_works.steps.step3.step'),
    title: t('home.how_it_works.steps.step3.title'),
    description: t('home.how_it_works.steps.step3.description'),
  },
  {
    id: 4,
    image: '/src/assets/images/how-it-works/phone-access.png',
    step: t('home.how_it_works.steps.step4.step'),
    title: t('home.how_it_works.steps.step4.title'),
    description: t('home.how_it_works.steps.step4.description'),
  },
]
</script>

<template>
  <section class="how-it-works">
    <h2 class="section-title">HOW DOES IT WORK?</h2>

    <div class="workflow-steps">
      <van-swipe :loop="false" :show-indicators="false">
        <van-swipe-item v-for="step in workflowSteps" :key="step.id">
          <div class="workflow-step">
            <div class="phone-container">
              <img :src="step.image" :alt="`Step ${step.id}: ${step.title}`" class="phone-image" />
            </div>

            <div class="step-indicator">
              <div class="step-number">{{ step.step }}</div>
            </div>

            <h3 class="step-title">{{ step.title }}</h3>

            <p class="step-description">{{ step.description }}</p>
          </div>
        </van-swipe-item>
      </van-swipe>
    </div>
  </section>
</template>

<style src="./HowItWorks.scss" lang="scss" scoped></style>
