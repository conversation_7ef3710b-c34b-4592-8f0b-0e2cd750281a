<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { ref } from 'vue'
import UserCarousel from './UserCarousel.vue'
import { getImageUrl } from '@/utils/getImageUrl'

const { t } = useI18n()
// 用户评价数据
const reviews = ref([
  {
    id: 1,
    avatar: '/src/assets/images/avatars/user1.jpg',
    name: '<PERSON>',
    date: '2023年12月21日',
    rating: 5,
    title: 'Great deadzone',
    content:
      'No need to get up and buy a sim card for the place your traveling to,just buy in app and enjoy the freedom of that worry.Its really easy and wonderful experience, and price is as good as can be plus they offer really cool coupons which came in handy!!!',
  },
  {
    id: 2,
    avatar: '/src/assets/images/avatars/user2.jpg',
    name: '<PERSON>',
    date: '2023年12月15日',
    rating: 5,
    title: 'Great deadzone',
    content:
      'No need to get up and buy a sim card for the place your traveling to,just buy in app and enjoy the freedom of that worry.Its really easy and wonderful experience, and price is as good as can be plus they offer really cool coupons which came in handy!!!',
  },
])

// 平台评分数据
const platforms = [
  {
    name: 'App Store',
    logo: 'appstore',
    rating: 4.7,
  },
  {
    name: 'Google Play',
    logo: 'playstore',
    rating: 4.7,
  },
  {
    name: 'Trustpilot',
    logo: 'trustpilot',
    rating: 4.7,
  },
]

// 生成星级评分
const generateStars = (rating) => {
  return Array.from({ length: 5 }, (_, i) => i < rating)
}
</script>

<template>
  <section class="user-reviews">
    <h2 class="section-title">{{ t('home.user_reviews.title') }}</h2>

    <!-- 平台评分 -->
    <div class="platform-ratings">
      <div v-for="platform in platforms" :key="platform.name" class="platform-rating">
        <div class="platform-logo">
          <img :src="getImageUrl(`${platform.logo}.svg`, 'svgs')" :alt="platform.name" />
        </div>
        <div class="rating-info">
          <div class="rating-score">{{ platform.rating }}<span class="rating-max">/5</span></div>
          <div class="rating-stars">
            <i
              v-for="(isFilled, index) in generateStars(5)"
              :key="index"
              class="star"
              :class="{ filled: isFilled }"
              >★</i
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 用户评价轮播 -->
    <div class="reviews-carousel">
      <UserCarousel :list="reviews" />
    </div>
  </section>
</template>

<style src="./UserReviews.scss" lang="scss" scoped></style>
