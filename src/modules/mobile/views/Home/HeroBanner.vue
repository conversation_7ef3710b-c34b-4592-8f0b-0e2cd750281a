<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { Swipe as Van<PERSON>wi<PERSON>, SwipeItem as VanSwipeItem } from 'vant'
import EsimSearch from '@/components/EsimSearch/index.vue'

const { t } = useI18n()
</script>

<template>
  <section class="hero-banner">
    <div class="hero-container">
      <div class="hero-content">
        <h1 class="hero-title">
          {{ t('home.title') }}
          <span class="hero-highlight">{{ t('home.title_highlight') }}</span>
        </h1>

        <!-- 搜索框 -->
        <EsimSearch size="mobile" placeholder="Search 200+ countries and regions..." />

        <!-- <div class="hero-img">
          <img src="../../images/BANNER_top_man.png" />
        </div> -->
      </div>
    </div>

    <!-- 底部促销条 -->
    <div class="promo-container">
      <van-swipe :loop="false" :width="300" :show-indicators="false">
        <van-swipe-item>
          <div class="promo-box adventure">
            <p class="promo-text fw-bold">{{ t('home.adventure') }}</p>
            <span class="promo-button">GCU50 </span>
          </div>
        </van-swipe-item>
        <van-swipe-item>
          <div class="promo-box share">
            <p class="promo-text fw-bold">{{ t('home.share_friend') }}</p>
            <span class="promo-button">{{ t('home.refer_friend.button') }} </span>
          </div>
        </van-swipe-item>
      </van-swipe>
    </div>
  </section>
</template>

<style src="./HeroBanner.scss" lang="scss" scoped></style>
