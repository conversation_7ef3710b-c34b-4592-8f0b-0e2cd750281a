<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import EsimShare from '@/components/EsimShare/index.vue'

const { t } = useI18n()

const handleLearnMore = () => {
  // 处理"Learn More"按钮点击事件
  console.log('Learn More clicked')
  // 可以添加导航到详情页面的逻辑
  // router.push('/package-details');
}
</script>

<template>
  <section class="hero-exclusive">
    <div class="exclusive-content">
      <div class="exclusive-tag">{{ t('home.exclusive.tag') }}</div>
      <h2 class="exclusive-title">{{ t('home.exclusive.title') }}</h2>
      <h3 class="exclusive-subtitle">{{ t('home.exclusive.subtitle') }}</h3>

      <EsimShare @click="handleLearnMore" size="small" mode="plain">
        <span class="fw-bold">{{ t('home.exclusive.button') }}</span>
      </EsimShare>
    </div>
  </section>
</template>

<style src="./Exclusive.scss" lang="scss" scoped></style>
