// 移动端DownloadApp样式将在这里添加
.download-app {
  text-align: center;
  height: 575px;
  background: url('../../images/BANNER_download_bg.png') center top no-repeat;
  background-size: 100%;
  overflow: hidden;
}

.text-content {
  padding-top: 36px;
  padding-bottom: 50px;
}

.section-title {
  color: #0c3d18;
  font-weight: bold;
  font-size: 24px;
  line-height: 30px;
  padding: 0 36px;
}

.section-description {
  margin-top: 8px;
  font-size: 16px;
  line-height: 22px;
  padding: 0 24px;
  color: #0c3d18;
}

.app-buttons {
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: center;

  .app-button {
    width: 145px;
    height: 48px;
    padding: 0;
    border-radius: 12px;
    color: #ffffff;
    background-color: #000000;
    & + .app-button {
      margin-left: 14px;
    }
    img {
      display: block;
    }
  }
}

.phone-mockup {
  img {
    display: block;
    height: 300px;
    max-width: 100%;
    margin: auto;
  }
}
