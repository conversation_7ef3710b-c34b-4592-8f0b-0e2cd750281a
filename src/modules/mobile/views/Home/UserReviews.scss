// 移动端UserReviews样式将在这里添加
.user-reviews {
  width: 100%;
  padding-top: 60px;
  padding-bottom: 16px;
}

.section-title {
  padding: 0 16px;
  font-size: 24px;
  line-height: 30px;
  font-weight: bold;
  text-align: center;
  color: var(--primary-color);
}

.platform-ratings {
  margin-top: 24px;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
}

.platform-rating {
  overflow: hidden;

  & + & {
    margin-left: 12px;
  }

  .platform-logo {
    width: 28px;
    height: 28px;
    margin-right: 4px;
    float: left;
    img {
      max-width: 100%;
      height: 28px;
    }
  }

  .rating-info {
    overflow: hidden;
  }

  .rating-score {
    color: var(--primary-color);
    font-size: 14px;
  }
  .rating-max {
    font-size: 12px;
  }

  .rating-stars {
    line-height: 1;
    i {
      width: 10px;
      height: 10px;
      gap: 2px;
      &.filled {
        color: #ffcc18;
      }
    }
  }
}

.reviews-carousel {
  margin-top: 24px;
  padding-bottom: 24px;
}
