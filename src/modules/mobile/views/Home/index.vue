<script setup lang="ts">
import HeroBanner from './HeroBanner.vue'
import PopularDestinations from './PopularDestinations.vue'
import UserReviews from './UserReviews.vue'
import DownloadApp from './DownloadApp.vue'
import ReferAFriend from './ReferAFriend.vue'
import HowItWorks from './HowItWorks.vue'
import Exclusive from './Exclusive.vue'
import Footer from '../../components/Footer.vue'
import Copyright from '../../components/Copyright.vue'
</script>

<template>
  <div class="home-page">
    <HeroBanner />
    <PopularDestinations />
    <ReferAFriend />
    <DownloadApp />
    <UserReviews />
    <HowItWorks />
    <Exclusive />
    <Footer />
    <Copyright />
  </div>
</template>

<style scoped lang="scss">
.home-page {
  width: 100%;
  background-color: var(--vt-c-white-soft);
}
</style>
