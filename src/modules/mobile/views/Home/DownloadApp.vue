<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { LINKS_DOWNLOAD as downloadLinks } from '@/const/links'
import { getImageUrl } from '@/utils/getImageUrl'
const { t } = useI18n()
</script>

<template>
  <section class="download-app">
    <div class="container">
      <div class="text-content">
        <h2 class="section-title">{{ t('home.download_app.title') }}</h2>
        <p class="section-description">
          {{ t('home.download_app.description') }}
        </p>
        <div class="app-buttons">
          <a
            v-for="item in downloadLinks"
            :key="item.name"
            :href="item.url"
            :title="t(item.name)"
            class="app-button"
          >
            <img :src="getImageUrl(`${item.icon}.svg`)" alt="" />
          </a>
        </div>
      </div>
      <div class="phone-mockup">
        <img src="../../images/BANNER_download_phone.png" alt="" />
      </div>
    </div>
  </section>
</template>

<style src="./DownloadApp.scss" lang="scss" scoped></style>
