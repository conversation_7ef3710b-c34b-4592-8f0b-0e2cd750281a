<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import EsimSlider from '@/components/EsimSlider/index.vue'
import EsimShare from '@/components/EsimShare/index.vue'
import EsimDestination from '@mobile/components/EsimDestination.vue'
import { destinations } from '@/data/destinations'

const { t } = useI18n()
const router = useRouter()

// 点击套餐信息
const handleClickCell = (item) => {
  router.push({
    name: 'plan-detail',
    params: {
      pid: item.id,
    },
    query: {
      name: item.name,
    },
  })
}
// 处理查看更多
const handleShowMore = () => {
  router.push({ name: 'shop-plans' })
}
</script>

<template>
  <section class="popular-destinations">
    <div class="container">
      <h2 class="section-title">{{ t('home.popular.title') }}</h2>

      <!-- 使用Vant的Tabs组件实现标签切换 -->
      <EsimSlider
        size="mobile"
        :list="[t('home.popular.local'), t('home.popular.regional')]"
      ></EsimSlider>

      <!-- 目的地网格 -->
      <EsimDestination :list="destinations" @click-cell="handleClickCell"></EsimDestination>

      <!-- 显示更多按钮 -->
      <div class="show-more-container">
        <EsimShare size="medium" @click="handleShowMore">Show More</EsimShare>
      </div>
    </div>
  </section>
</template>

<style src="./PopularDestinations.scss" lang="scss" scoped></style>
