<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { showLoadingToast, closeToast, showToast } from 'vant'

const { t } = useI18n()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  initialValue: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:visible', 'save'])

const name = ref('')
const isLoading = ref(false)

const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})
// 监听visible变化，当弹框打开时初始化name值
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      name.value = props.initialValue
    }
  },
)

// 监听initialValue变化
watch(
  () => props.initialValue,
  (newVal) => {
    name.value = newVal
  },
)

// 关闭弹框
const closeDialog = () => {
  modalVisible.value = false
}

// 清空输入
const clearInput = () => {
  name.value = ''
}

// 保存姓名
const saveName = async () => {
  if (!name.value.trim()) {
    showToast(t('editName.nameRequired'))
    return
  }

  try {
    isLoading.value = true
    showLoadingToast({
      message: t('editName.saving'),
      forbidClick: true,
    })

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    emit('save', name.value)
    closeDialog()
    showToast(t('editName.saveSuccess'))
  } catch (error) {
    showToast(t('editName.saveFailed'))
  } finally {
    isLoading.value = false
    closeToast()
  }
}
</script>

<template>
  <van-popup
    v-model:show="modalVisible"
    round
    position="center"
    :close-on-click-overlay="false"
    class="edit-name-dialog"
  >
    <div class="dialog-content">
      <h2 class="dialog-title">{{ t('editName.title') }}</h2>

      <div class="input-container">
        <van-field
          v-model="name"
          :placeholder="t('editName.placeholder')"
          class="name-input"
          :border="false"
          clearable
          @clear="clearInput"
        />
      </div>

      <div class="dialog-footer">
        <van-button class="cancel-btn" plain block @click="closeDialog">
          {{ t('editName.cancel') }}
        </van-button>

        <van-button class="save-btn" block :loading="isLoading" @click="saveName">
          {{ t('editName.save') }}
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.edit-name-dialog {
  width: 90%;
  max-width: 340px;

  :deep(.van-popup__close-icon) {
    color: #999;
  }
}

.dialog-content {
  padding: 24px;
}

.dialog-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 24px;
  text-align: left;
}

.input-container {
  margin-bottom: 32px;

  .name-input {
    --van-field-input-text-color: #000;
    --van-field-placeholder-text-color: rgba(0, 0, 0, 0.5);
    --van-field-clear-icon-color: #999;

    border: 1px solid #00c851;
    border-radius: 100px;
    padding: 0 16px;
    height: 56px;
    box-shadow: 0 0 0 2px rgba(0, 200, 81, 0.1);

    :deep(.van-field__control) {
      height: 54px;
      font-size: 16px;
    }

    :deep(.van-field__right-icon) {
      padding-right: 8px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  gap: 16px;

  .cancel-btn {
    flex: 1;
    height: 56px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 100px;
    border: none;
    color: #000;

    &:active {
      opacity: 0.7;
    }
  }

  .save-btn {
    flex: 1;
    height: 56px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 100px;
    background-color: #000;
    border: none;
    color: #fff;

    &:active {
      opacity: 0.9;
    }
  }
}
</style>
