<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { Icon } from '@/components/Icon'
import PageHeader from '@mobile/components/HeaderTitle.vue'
import EsimEmpty from '@mobile/components/EsimEmpty.vue'
import type { PointsTransaction } from '@/api/Points/types.ts'

const { t } = useI18n()
const router = useRouter()

// 用户积分信息 - 复用web版的数据
const pointsInfo = ref({
  totalPoints: 12030,
  availablePoints: 12030,
  pointsBalance: 120.0,
  currencyType: 'USD',
  exchangeRate: 100, // 100积分 = 1美元
})

// 模拟积分历史数据 - 复用web版的数据
const pointsHistory = ref<PointsTransaction[]>([
  {
    transactionId: '1',
    transactionType: 'EARN',
    pointsAmount: 10,
    description: 'Purchase rewards',
    transactionTime: Date.parse('2025/01/01'),
    status: 'COMPLETED',
  },
  {
    transactionId: '2',
    transactionType: 'EARN',
    pointsAmount: 10,
    description: 'Purchase rewards',
    transactionTime: Date.parse('2025/01/02'),
    status: 'COMPLETED',
  },
  {
    transactionId: '3',
    transactionType: 'EARN',
    pointsAmount: 10,
    description: 'Purchase rewards',
    transactionTime: Date.parse('2025/01/03'),
    status: 'COMPLETED',
  },
  {
    transactionId: '4',
    transactionType: 'EARN',
    pointsAmount: 10,
    description: 'Purchase rewards',
    transactionTime: Date.parse('2025/01/04'),
    status: 'COMPLETED',
  },
  {
    transactionId: '5',
    transactionType: 'EARN',
    pointsAmount: 10,
    description: 'Purchase rewards',
    transactionTime: Date.parse('2025/01/05'),
    status: 'COMPLETED',
  },
])

// 格式化日期 - 复用web版的函数
const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleDateString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

// 下载应用
const handleDownloadApp = () => {
  console.log('Download GlocalMe App')
  // 这里应该跳转到应用下载页面
}

onMounted(() => {
  console.log('Mobile Points & Rewards page mounted')
})
</script>

<template>
  <PageHeader :title="t('pointsRewards.title')"></PageHeader>

  <div class="points-rewards-page">
    <!-- 积分余额卡片 -->
    <div class="points-balance-card">
      <div class="balance-section">
        <div class="balance-item">
          <div class="balance-value">{{ pointsInfo.totalPoints.toLocaleString() }}</div>
          <div class="balance-label">
            <Icon icon="svg-icon:star" :size="14" />
            {{ t('pointsRewards.points') }}
          </div>
        </div>

        <div class="divider"></div>

        <div class="balance-item">
          <div class="balance-value">${{ pointsInfo.pointsBalance.toFixed(2) }}</div>
          <div class="balance-label">
            <Icon icon="svg-icon:money" :size="14" />
            {{ t('pointsRewards.balance') }}
          </div>
        </div>
      </div>

      <!-- 兑换产品提示 -->
      <div class="redeem-info">
        <div class="redeem-title">
          <Icon icon="svg-icon:promo" :size="16" />
          {{ t('pointsRewards.redeemTitle') }}
        </div>

        <p class="redeem-description">
          {{ t('pointsRewards.redeemDescription') }}
        </p>

        <van-button class="download-app-btn" plain block @click="handleDownloadApp">
          {{ t('pointsRewards.downloadApp') }}
        </van-button>
      </div>
    </div>

    <!-- 购买历史标题 -->
    <div class="section-title">
      {{ t('pointsRewards.purchaseHistory') }}
    </div>

    <!-- 购买历史列表 -->
    <van-cell-group v-if="pointsHistory && pointsHistory.length > 0" inset class="history-list">
      <van-cell
        v-for="transaction in pointsHistory"
        :key="transaction.transactionId"
        center
        :title="t('pointsRewards.purchaseRewards')"
        :label="formatDate(transaction.transactionTime)"
        :value="`+ ${transaction.pointsAmount} ${t('pointsRewards.pointsEarned')}`"
      />
    </van-cell-group>
    <EsimEmpty v-else />
  </div>
</template>

<style scoped lang="scss">
.points-rewards-page {
  padding: 16px 16px 80px;
}

.points-balance-card {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  padding: 24px 0;
  margin-bottom: 24px;
}

.balance-section {
  display: flex;
  padding: 24px 0;
}

.balance-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.divider {
  width: 1px;
  background-color: #e0e0e0;
  margin: 0 16px;
}

.balance-value {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8px;
}

.balance-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #000000;
}

.redeem-info {
  margin-top: 24px;
  padding: 0 16px;
}

.redeem-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8px;
}

.redeem-description {
  font-size: 14px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 24px;
}

.download-app-btn {
  --van-button-radius: 8px;
  --van-button-default-height: 36px;
  --van-button-default-font-size: 14px;
  --van-button-default-border-color: #000000;

  width: 200px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin: 24px 0 12px;
}

.history-list {
  --van-cell-group-inset-padding: 0;
  --van-cell-vertical-padding: 15px;
  --van-cell-text-color: #000000;
  --van-cell-label-color: rgba(0, 0, 0, 0.5);
  --van-cell-value-color: #000000;

  :deep(.van-cell__title) span {
    font-weight: 600;
  }
}
</style>
