<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import PageHeader from '@mobile/components/HeaderTitle.vue'
import { Icon } from '@/components/Icon/index'

const { t } = useI18n()
const router = useRouter()

// 表单数据
const verificationCode = ref('')
const newPassword = ref('')
const retypePassword = ref('')
const isLoading = ref(false)
const codeSent = ref(false)
const countdown = ref(0)
const timer = ref<number | null>(null)

// 密码可见性控制
const showNewPassword = ref(false)
const showRetypePassword = ref(false)

// 表单验证
const codeValid = computed(() => verificationCode.value.length === 6)
const passwordValid = computed(() => {
  const password = newPassword.value
  if (password.length < 6 || password.length > 20) return false

  // 检查是否包含至少两种字符类型：字母、数字或符号
  let hasLetter = /[a-zA-Z]/.test(password)
  let hasNumber = /[0-9]/.test(password)
  let hasSymbol = /[^a-zA-Z0-9]/.test(password)

  let typeCount = 0
  if (hasLetter) typeCount++
  if (hasNumber) typeCount++
  if (hasSymbol) typeCount++

  return typeCount >= 2
})

const passwordsMatch = computed(
  () => newPassword.value && retypePassword.value && newPassword.value === retypePassword.value,
)

const canSave = computed(
  () => codeValid.value && passwordValid.value && passwordsMatch.value && !isLoading.value,
)

// 发送验证码
const sendVerificationCode = async () => {
  if (countdown.value > 0) return

  try {
    isLoading.value = true
    showLoadingToast({
      message: t('editPassword.sendingCode'),
      forbidClick: true,
    })

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1500))

    codeSent.value = true
    startCountdown()
    showToast(t('editPassword.codeSent'))
  } catch (error) {
    showToast(t('editPassword.sendCodeFailed'))
  } finally {
    isLoading.value = false
    closeToast()
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  if (timer.value) clearInterval(timer.value)

  timer.value = window.setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
  }, 1000)
}

// 保存新密码
const savePassword = async () => {
  if (!canSave.value) return

  try {
    isLoading.value = true
    showLoadingToast({
      message: t('editPassword.saving'),
      forbidClick: true,
    })

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 2000))

    showToast(t('editPassword.saveSuccess'))
    setTimeout(() => {
      router.back()
    }, 1500)
  } catch (error) {
    showToast(t('editPassword.saveFailed'))
  } finally {
    isLoading.value = false
    closeToast()
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="edit-password-container">
    <PageHeader :title="t('editPassword.title')"></PageHeader>

    <div class="edit-password-page">
      <p class="verification-message">
        {{ t('editPassword.verificationMessage') }}
      </p>

      <div class="form-group">
        <div class="verification-row">
          <van-field
            v-model="verificationCode"
            :placeholder="t('editPassword.verificationCode')"
            type="number"
            maxlength="6"
            class="verification-input"
          />

          <van-button
            class="send-code-btn"
            :disabled="countdown > 0"
            :loading="isLoading"
            @click="sendVerificationCode"
          >
            {{ countdown > 0 ? `${countdown}s` : t('editPassword.sendCode') }}
          </van-button>
        </div>

        <van-field
          v-model="newPassword"
          :placeholder="t('editPassword.newPassword')"
          :type="showNewPassword ? 'text' : 'password'"
          class="password-input"
        >
          <template #right-icon>
            <Icon
              :icon="showNewPassword ? 'svg-icon:eye_open' : 'svg-icon:eye_close'"
              :size="20"
              @click="showNewPassword = !showNewPassword"
            />
          </template>
        </van-field>

        <van-field
          v-model="retypePassword"
          :placeholder="t('editPassword.retypePassword')"
          :type="showRetypePassword ? 'text' : 'password'"
          class="password-input"
        >
          <template #right-icon>
            <Icon
              :icon="showRetypePassword ? 'svg-icon:eye_open' : 'svg-icon:eye_close'"
              :size="20"
              @click="showRetypePassword = !showRetypePassword"
            />
          </template>
        </van-field>

        <p class="password-requirements">
          {{ t('editPassword.passwordRequirements') }}
        </p>
      </div>

      <van-button
        block
        round
        :disabled="!canSave"
        :loading="isLoading"
        class="save-btn"
        @click="savePassword"
      >
        {{ t('editPassword.save') }}
      </van-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.edit-password-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.edit-password-page {
  padding: 16px 16px 80px;
}

.verification-message {
  font-size: 16px;
  line-height: 1.5;
  color: #000000;
  margin-bottom: 24px;
}

.form-group {
  background-color: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 32px;
}

.verification-row {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.verification-input {
  flex: 1;

  :deep(.van-field__control) {
    height: 56px;
  }
}

.send-code-btn {
  flex-shrink: 0;
  height: 40px;
  margin-right: 16px;
  border-radius: 20px;
  font-size: 14px;
  padding: 0 16px;
  background-color: #00c851;
  color: #ffffff;
  border: none;

  &:disabled {
    background-color: #e0e0e0;
    color: #999999;
  }
}

.password-input {
  :deep(.van-field__control) {
    height: 56px;
  }

  :deep(.van-field__right-icon) {
    padding: 0 16px;
    cursor: pointer;
  }
}

.password-requirements {
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.5);
  padding: 16px;
  margin: 0;
}

.save-btn {
  height: 56px;
  font-size: 16px;
  font-weight: 500;
  background-color: #cccccc;
  border: none;
  color: #ffffff;

  &:not(:disabled) {
    background-color: #000000;
  }
}
</style>
