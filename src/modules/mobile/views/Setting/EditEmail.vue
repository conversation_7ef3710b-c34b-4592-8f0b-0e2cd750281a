<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import PageHeader from '@mobile/components/HeaderTitle.vue'

const { t } = useI18n()
const router = useRouter()

// 表单数据
const verificationCode = ref('')
const newEmail = ref('')
const isLoading = ref(false)
const codeSent = ref(false)
const countdown = ref(0)
const timer = ref<number | null>(null)

// 表单验证
const codeValid = computed(() => verificationCode.value.length === 6)
const emailValid = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(newEmail.value)
})

const canSave = computed(() => codeValid.value && emailValid.value && !isLoading.value)

// 发送验证码
const sendVerificationCode = async () => {
  if (countdown.value > 0) return

  try {
    isLoading.value = true
    showLoadingToast({
      message: t('editEmail.sendingCode'),
      forbidClick: true,
    })

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1500))

    codeSent.value = true
    startCountdown()
    showToast(t('editEmail.codeSent'))
  } catch (error) {
    showToast(t('editEmail.sendCodeFailed'))
  } finally {
    isLoading.value = false
    closeToast()
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  if (timer.value) clearInterval(timer.value)

  timer.value = window.setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
  }, 1000)
}

// 保存新邮箱
const saveEmail = async () => {
  if (!canSave.value) return

  try {
    isLoading.value = true
    showLoadingToast({
      message: t('editEmail.saving'),
      forbidClick: true,
    })

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 2000))

    showToast(t('editEmail.saveSuccess'))
    setTimeout(() => {
      router.back()
    }, 1500)
  } catch (error) {
    showToast(t('editEmail.saveFailed'))
  } finally {
    isLoading.value = false
    closeToast()
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="edit-email-container">
    <PageHeader :title="t('editEmail.title')"></PageHeader>

    <div class="edit-email-page">
      <p class="verification-message">
        {{ t('editEmail.verificationMessage') }}
      </p>

      <div class="form-group">
        <div class="verification-row">
          <van-field
            v-model="verificationCode"
            :placeholder="t('editEmail.verificationCode')"
            type="number"
            maxlength="6"
            class="verification-input"
          />

          <van-button
            class="send-code-btn"
            :disabled="countdown > 0"
            :loading="isLoading"
            @click="sendVerificationCode"
          >
            {{ countdown > 0 ? `${countdown}s` : t('editEmail.sendCode') }}
          </van-button>
        </div>

        <van-field
          v-model="newEmail"
          :placeholder="t('editEmail.newEmail')"
          type="email"
          class="email-input"
        />
      </div>

      <van-button
        block
        round
        :disabled="!canSave"
        :loading="isLoading"
        class="save-btn"
        @click="saveEmail"
      >
        {{ t('editEmail.save') }}
      </van-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.edit-email-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.edit-email-page {
  padding: 16px 16px 80px;
}

.verification-message {
  font-size: 16px;
  line-height: 1.5;
  color: #000000;
  margin-bottom: 24px;
}

.form-group {
  background-color: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 32px;
}

.verification-row {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.verification-input {
  flex: 1;

  :deep(.van-field__control) {
    height: 56px;
  }
}

.send-code-btn {
  flex-shrink: 0;
  height: 40px;
  margin-right: 16px;
  border-radius: 20px;
  font-size: 14px;
  padding: 0 16px;
  background-color: #00c851;
  color: #ffffff;
  border: none;

  &:disabled {
    background-color: #e0e0e0;
    color: #999999;
  }
}

.email-input {
  :deep(.van-field__control) {
    height: 56px;
  }
}

.save-btn {
  height: 56px;
  font-size: 16px;
  font-weight: 500;
  background-color: #cccccc;
  border: none;
  color: #ffffff;

  &:not(:disabled) {
    background-color: #000000;
  }
}
</style>
