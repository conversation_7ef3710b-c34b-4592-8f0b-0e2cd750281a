<script lang="ts" setup>
import PageHeader from '@/modules/mobile/components/HeaderTitle.vue'
import FixedBottom from '@mobile/components/FixedBottom.vue'
import PromoCard from '@mobile/components/PromoCard.vue'
import EsimEmpty from '@mobile/components/EsimEmpty.vue'
import { promos } from '@/data/promos'
import { useI18n } from 'vue-i18n'
import { computed } from 'vue'

const { t } = useI18n()

const allPromos = computed(() => {
  return promos.map((item) => {
    item.isBestSaving = false
    item.isDisabled = false
    item.isSelected = false
    return item
  })
})

// 打开promo code弹框
const handleClick = () => {}
</script>

<template>
  <PageHeader :title="t('myPromos.title')"></PageHeader>

  <div class="my-promos-page">
    <template v-if="allPromos && allPromos.length > 0">
      <PromoCard
        v-for="promo in allPromos"
        :key="promo.id"
        :promo="promo"
        :readonly="true"
      ></PromoCard>
    </template>
    <EsimEmpty v-else description="No promo available." />
  </div>

  <FixedBottom text="REDEEM VOUCHER" @click-button="handleClick"></FixedBottom>
</template>

<style lang="scss" scoped>
.my-promos-page {
  padding: 16px 16px 100px;
}
</style>
