<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { Icon } from '@/components/Icon'
import PageHeader from '@mobile/components/HeaderTitle.vue'
import EsimEmpty from '@mobile/components/EsimEmpty.vue'
import type { OrderItem } from '@/api/Order/types.ts'

const { t } = useI18n()
const router = useRouter()

// 复用web版的订单数据
const orders = ref<OrderItem[]>([
  {
    orderId: '1',
    orderSN: 'ORD001',
    orderName: '5GB 3-Day for United States',
    orderStatus: 'COMPLETED',
    orderType: 'TOPUP',
    orderAmount: 900,
    currencyType: 'USD',
    createTime: Date.parse('2025/01/01'),
    payTime: Date.parse('2025/01/01'),
    completeTime: Date.parse('2025/01/01'),
  },
  {
    orderId: '2',
    orderSN: 'ORD002',
    orderName: '4GB 3-Day for Australia',
    orderStatus: 'COMPLETED',
    orderType: 'TOPUP',
    orderAmount: 1800,
    currencyType: 'USD',
    createTime: Date.parse('2025/01/01'),
    payTime: Date.parse('2025/01/01'),
    completeTime: Date.parse('2025/01/01'),
  },
  {
    orderId: '3',
    orderSN: 'ORD003',
    orderName: '20GB 3-Day for United Kingdom',
    orderStatus: 'COMPLETED',
    orderType: 'TOPUP',
    orderAmount: 18000,
    currencyType: 'USD',
    createTime: Date.parse('2025/01/01'),
    payTime: Date.parse('2025/01/01'),
    completeTime: Date.parse('2025/01/01'),
  },
  {
    orderId: '4',
    orderSN: 'ORD004',
    orderName: '4GB 3-Day for United States4GB 3-Day for United States',
    orderStatus: 'COMPLETED',
    orderType: 'TOPUP',
    orderAmount: 1588,
    currencyType: 'USD',
    createTime: Date.parse('2025/01/01'),
    payTime: Date.parse('2025/01/01'),
    completeTime: Date.parse('2025/01/01'),
  },
  {
    orderId: '5',
    orderSN: 'ORD005',
    orderName: '5GB 3-Day for United States',
    orderStatus: 'COMPLETED',
    orderType: 'TOPUP',
    orderAmount: 899,
    currencyType: 'USD',
    createTime: Date.parse('2025/01/01'),
    payTime: Date.parse('2025/01/01'),
    completeTime: Date.parse('2025/01/01'),
  },
])

// 格式化金额
const formatAmount = (amount: number, currency: string) => {
  const value = (amount / 100).toFixed(2)
  return `${currency} ${value}`
}

// 格式化日期
const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleDateString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

// 处理订单点击
const handleOrderClick = (order: OrderItem) => {
  console.log('Order clicked:', order)
  // 这里可以跳转到订单详情页面
  router.push(`/order-detail/${order.orderId}`)
}

// 处理购买套餐
const handleShopPlans = () => {
  router.push('/shop-plans')
}

onMounted(() => {
  // 这里应该调用API获取订单数据
  console.log('Mobile Orders page mounted')
})
</script>

<template>
  <PageHeader :title="t('orders.title')"></PageHeader>

  <div class="orders-page">
    <template v-if="orders && orders.length > 0">
      <div class="orders-list">
        <div
          v-for="order in orders"
          :key="order.orderId"
          class="order-item"
          @click="handleOrderClick(order)"
        >
          <div class="order-content">
            <div class="order-info">
              <h3 class="order-name">{{ order.orderName }}</h3>
              <p class="order-date">{{ formatDate(order.createTime) }}</p>
            </div>
            <div class="order-right">
              <div class="order-amount">
                {{ formatAmount(order.orderAmount, order.currencyType) }}
              </div>
              <div class="order-arrow">
                <Icon icon="svg-icon:arrow_right" :size="20" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div v-else class="order-empty">
      <EsimEmpty description="No orders found." />
      <van-button type="primary" size="large" block round class="shop-btn" @click="handleShopPlans">
        {{ t('myEsims.empty.shopPlans') }}
      </van-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.orders-page {
  padding: 16px 16px 100px;
}

.order-item {
  background-color: #ffffff;
  border-radius: 12px;
  line-height: 1;
  padding: 20px 16px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-info {
  flex: 1;
  min-width: 0;
}

.order-name {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  color: #000000;
}

.order-date {
  margin-top: 12px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.5);
}

.order-right {
  display: flex;
  align-items: center;
  margin-left: 6px;
}

.order-amount {
  font-size: 14px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.7);
  margin-right: 8px;
  white-space: nowrap;
}

.order-arrow {
  color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
}

.order-empty {
  padding-top: 20px;

  .shop-btn {
    margin: 24px auto 0;
    width: 220px;
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
