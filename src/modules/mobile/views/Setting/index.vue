<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import PageHeader from '@mobile/components/HeaderTitle.vue'
import EditNameDialog from './EditNameDialog.vue'

const { t } = useI18n()
const router = useRouter()

// 用户信息数据
const userInfo = reactive({
  fullName: '<PERSON> Blunt',
  username: 'GlocalMe001',
  email: '<EMAIL>',
  password: '••••••••••••',
  placeOfResidence: 'United States',
  marketingEmails: true,
  avatar: '', // 头像占位符，使用CSS生成
  isVip: true,
  isUt: true,
})

// 编辑姓名弹框控制
const editNameDialogVisible = ref(false)

// 打开编辑姓名弹框
const handleEditName = () => {
  editNameDialogVisible.value = true
}

// 保存姓名
const handleSaveName = (newName: string) => {
  userInfo.fullName = newName
  showToast(t('accountSettings.nameUpdated'))
}

// 编辑密码
const handleEditPassword = () => {
  router.push('/edit-password')
}

// 编辑邮箱
const handleEditEmail = () => {
  router.push('/edit-email')
}
</script>

<template>
  <PageHeader :title="t('accountSettings.title')"></PageHeader>

  <div class="account-settings">
    <van-cell-group inset>
      <van-cell center :title="t('accountSettings.fullName')" :label="userInfo.fullName">
        <template #right-icon>
          <van-button plain round type="primary" @click="handleEditName">Edit</van-button>
        </template>
      </van-cell>
    </van-cell-group>
    <van-cell-group inset>
      <van-cell center :title="t('accountSettings.emailAddress')" :label="userInfo.email">
        <template #right-icon>
          <van-button plain round type="primary" @click="handleEditEmail">Edit</van-button>
        </template>
      </van-cell>
    </van-cell-group>
    <van-cell-group inset>
      <van-cell center :title="t('accountSettings.password')" :label="userInfo.password">
        <template #right-icon>
          <van-button plain round type="primary" @click="handleEditPassword">Edit</van-button>
        </template>
      </van-cell>
    </van-cell-group>
    <van-cell-group inset>
      <van-cell
        center
        :title="t('accountSettings.placeOfResidence')"
        :label="userInfo.placeOfResidence"
      />
    </van-cell-group>
    <van-cell-group inset class="switch">
      <van-cell center :title="t('accountSettings.marketingEmails')">
        <template #right-icon>
          <van-switch v-model="userInfo.marketingEmails" active-color="var(--base-color)" />
        </template>
      </van-cell>
    </van-cell-group>
  </div>

  <!-- 编辑姓名弹框 -->
  <EditNameDialog
    v-model:visible="editNameDialogVisible"
    :initial-value="userInfo.fullName"
    @save="handleSaveName"
  />
</template>

<style lang="scss" scoped>
.account-settings {
  :deep(.van-button) {
    --van-button-normal-padding: 0 18px;
    --van-button-default-height: 28px;
  }

  :deep(.van-cell-group) {
    --van-cell-line-height: 1;
    --van-cell-group-inset-radius: 12px;
    --van-cell-vertical-padding: 23px;
    --van-cell-text-color: rgba(0, 0, 0, 0.5);
    --van-cell-font-size: 12px;

    --van-cell-label-font-size: 16px;
    --van-cell-label-color: var(--primary-color);
    --van-cell-label-margin-top: 12px;
    margin-bottom: 12px;

    .van-cell__value {
      flex: none;
    }
  }

  .switch {
    --van-cell-text-color: var(--primary-color);
    --van-cell-font-size: 14px;
  }
}
</style>
