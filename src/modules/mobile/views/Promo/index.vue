<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import PageHeader from '@/modules/mobile/components/HeaderTitle.vue'
import FixedBottom from '@mobile/components/FixedBottom.vue'
import PromoCard from '@mobile/components/PromoCard.vue'
import { type PromoCode, promos } from '@/data/promos'

const router = useRouter()
const { t } = useI18n()

const allPromos = ref<PromoCode[]>(promos)
const availablePromos = computed(() => allPromos.value.filter((item) => !item.isDisabled))
const conditionalPromos = computed(() => allPromos.value.filter((item) => item.isDisabled))

// 选择优惠码
const selectPromo = (promo: PromoCode, section: 'available' | 'conditional') => {
  if (promo.isDisabled) return

  allPromos.value.forEach((p) => {
    return (p.isSelected = p.id === promo.id)
  })
}

// 使用选中的优惠码
const useSelectedPromo = () => {
  const selectedPromo = allPromos.value.find((p) => p.isSelected)
  if (selectedPromo) {
    console.log('使用优惠码:', selectedPromo.code)
    // TODO: 实现优惠码应用逻辑，可能需要跳转到结账页面或调用API
    router.back()
  }
}
</script>

<template>
  <div class="promo-page">
    <!-- 顶部导航 -->
    <PageHeader :title="t('promo.title')" />

    <!-- 可用优惠码 -->
    <div class="promo-section">
      <h2 class="section-title">
        {{ t('promo.available_promos') }} ({{ availablePromos.length }})
      </h2>

      <div class="promo-cards">
        <PromoCard
          v-for="promo in availablePromos"
          :key="promo.id"
          :promo="promo"
          @selected="selectPromo"
        >
        </PromoCard>
      </div>
    </div>

    <!-- 有条件限制的优惠码 -->
    <div class="promo-section" v-if="conditionalPromos.length > 0">
      <h2 class="section-title">
        {{ t('promo.unavailable_promos') }} ({{ conditionalPromos.length }})
      </h2>

      <div class="promo-cards">
        <PromoCard
          v-for="promo in conditionalPromos"
          :key="promo.id"
          :promo="promo"
          @selected="selectPromo"
        >
        </PromoCard>
      </div>
    </div>

    <!-- 底部按钮 -->
    <FixedBottom :text="t('promo.use_promo')" @click-button="useSelectedPromo"></FixedBottom>
  </div>
</template>

<style src="./Promo.scss" lang="scss" scoped></style>
