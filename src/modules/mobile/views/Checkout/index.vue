<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Icon } from '@/components/Icon'
import PageHeader from '@/modules/mobile/components/HeaderTitle.vue'
import FixedBottom from '@mobile/components/FixedBottom.vue'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()

// 订单信息
const orderInfo = ref({
  id: Number(route.params.id) || 1,
  country: 'United States',
  data: '10GB',
  days: '3-Day',
  subtotal: 10.0,
  discount: 0.5,
  activationPeriod: '30-Day',
  activationDeadline: 'April 26',
})

// 优惠码相关
const promoCode = ref('')
const isPromoApplied = ref(true)
const promoCount = ref(1)
const discountPercent = ref(50)

// 计算最终价格
const finalTotal = computed(() => {
  return orderInfo.value.subtotal - (isPromoApplied.value ? orderInfo.value.discount : 0)
})

// 显示优惠码模态框
const showPromoModal = () => {
  console.log('Show promo modal')
}

// 应用优惠码
const applyPromoCode = () => {
  if (promoCode.value) {
    isPromoApplied.value = true
    console.log('Applied promo code:', promoCode.value)
  }
}

// 移除优惠码
const removePromoCode = () => {
  promoCode.value = ''
  isPromoApplied.value = false
}

// 处理下单
const handlePlaceOrder = () => {
  console.log('Place order with total:', finalTotal.value)
  // 这里可以添加下单逻辑
  router.push({ name: 'payment' })
}
</script>

<template>
  <div class="checkout-page">
    <PageHeader :title="t('checkout.title')" />

    <div class="checkout-page-content">
      <!-- 产品信息 -->
      <div class="product-card">
        <div class="product-name">
          [eSIM] {{ orderInfo.data }} {{ orderInfo.days }} Package for {{ orderInfo.country }}
        </div>
      </div>

      <!-- 优惠码区域 -->
      <div class="promo-section">
        <div class="section-header">
          <h3 class="section-title">{{ t('checkout.promo') }}</h3>
          <RouterLink :to="{ name: 'checkout-promo' }" class="promo-link">
            {{ t('checkout.my_promo') }}({{ promoCount }} {{ t('promo.available_promos') }})
          </RouterLink>
        </div>

        <div class="promo-input-group">
          <div class="promo-icon">
            <Icon icon="svg-icon:promo" size="24" />
          </div>
          <input
            v-model="promoCode"
            class="promo-input"
            type="text"
            :placeholder="t('promo.promo_code')"
          />
          <div class="promo-suffix">
            <button
              v-if="!isPromoApplied"
              class="apply-button"
              :disabled="!promoCode"
              @click="applyPromoCode"
            >
              {{ t('checkout.apply') }}
            </button>
            <div v-else class="promo-applied">
              <span class="onsale"> {{ discountPercent }}% off </span>
              <Icon icon="svg-icon:close" size="20" class="close-icon" @click="removePromoCode" />
            </div>
          </div>
        </div>
      </div>

      <!-- 订单摘要 -->
      <div class="order-summary">
        <div class="section-header">
          <h3 class="section-title">{{ t('checkout.order_summary') }}</h3>
        </div>

        <div class="order-group">
          <div class="group-cell">
            <div class="summary-item">
              <span class="item-label">1 {{ t('checkout.item') }}</span>
            </div>
          </div>

          <div class="group-cell">
            <div class="summary-item">
              <span class="item-label">{{ t('checkout.subtotal') }}</span>
              <span class="item-value">USD {{ orderInfo.subtotal.toFixed(2) }}</span>
            </div>

            <div v-if="isPromoApplied" class="summary-item">
              <span class="item-label">{{ t('checkout.vip_discount') }}</span>
              <span class="item-value discount">-USD {{ orderInfo.discount.toFixed(1) }}</span>
            </div>
          </div>

          <div class="group-cell">
            <div class="summary-item total">
              <span class="item-label">{{ t('checkout.total') }}</span>
              <span class="item-value">USD {{ finalTotal.toFixed(2) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 激活信息 -->
      <div class="activation-info-section">
        <div class="activation-header">
          <Icon icon="svg-icon:question" size="14" class="question-icon" />
          <h3 class="activation-title">{{ t('checkout.activate_later') }}</h3>
        </div>

        <p class="activation-text">
          {{
            t('checkout.activation_period_text', {
              period: orderInfo.activationPeriod,
              deadline: orderInfo.activationDeadline,
            })
          }}
        </p>
      </div>
    </div>

    <!-- 底部结算栏 -->
    <FixedBottom align="between">
      <div class="checkout-total flex-1">
        <div class="total-label">{{ t('checkout.total') }}</div>
        <div class="total-price">USD {{ finalTotal.toFixed(2) }}</div>
      </div>

      <div class="flex-1">
        <van-button type="primary" size="large" block round @click="handlePlaceOrder">
          {{ t('checkout.place_order') }}
        </van-button>
      </div>
    </FixedBottom>
  </div>
</template>

<style lang="scss" scoped>
@import './Checkout.scss';
</style>
