.checkout-page {
  padding-bottom: 72px;
  &-content {
    padding: 16px;
  }
}

.section-title {
  font-size: 16px;
  line-height: 1;
  font-weight: 600;
  color: #000;
}

// 产品信息样式
.product-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 18px 16px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  line-height: 20px;
}

// 优惠码区域样式
.promo-section {
  margin-bottom: 24px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .promo-link {
      color: var(--base-color);
      font-size: 14px;
      text-decoration: underline;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .promo-input-group {
    position: relative;
    width: 100%;
    height: 56px;

    &:before {
      content: '';
      width: 1px;
      height: 24px;
      background: #e0e0e0;
      position: absolute;
      left: 56px;
      top: 50%;
      margin-top: -12px;
      z-index: 2;
    }

    .promo-icon {
      width: 24px;
      color: #333333;
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 2;
    }

    .promo-suffix {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 2;
    }

    .promo-input {
      background: #ffffff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      width: 100%;
      height: 100%;
      border: 1px solid transparent;
      font-size: 14px;
      color: #000;
      padding-left: 72px;
      padding-right: 130px;

      &:focus {
        outline: none;
        border-color: var(--base-color);
        box-shadow: 0 2px 12px rgba(0, 198, 94, 0.15);
      }

      &::placeholder {
        color: #999;
      }
    }

    .apply-button {
      background-color: rgb(0 198 94 / 6%);
      border: 1px solid var(--base-color);
      color: var(--base-color);
      padding: 8px 13px;
      border-radius: 20px;
      font-size: 14px;
      line-height: 1;
      font-weight: 500;

      &:disabled {
        opacity: 0.5;
        background-color: transparent;
        border-color: #ccc;
        color: #ccc;
      }
    }

    .promo-applied {
      display: flex;
      align-items: center;
    }

    .onsale {
      color: #ff2424;
      font-size: 18px;
      line-height: 1;
      margin-right: 24px;
    }

    .close-icon {
      color: #999;
    }
  }
}

// 订单摘要样式
.order-summary {
  margin-bottom: 24px;

  .section-header {
    margin-bottom: 12px;
  }

  .order-group {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    padding: 0 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    .group-cell {
      min-height: 56px;
      padding: 19px 0;
      line-height: 1;
      font-size: 14px;
      color: #000000;

      & + .group-cell {
        border-top: 1px solid #e0e0e0;
      }
    }
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;

    & + .summary-item {
      margin-top: 16px;
    }

    .item-value {
      &.discount {
        color: #ff2424;
      }
    }

    &.total {
      .item-label {
        font-weight: 600;
      }
      .item-value {
        font-size: 18px;
        font-weight: 600;
      }
    }
  }
}

// 激活信息样式
.activation-info-section {
  margin-bottom: 60px;

  .activation-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .question-icon {
      margin-right: 8px;
      color: #666;
    }

    .activation-title {
      font-size: 14px;
      font-weight: 600;
      color: #000000;
    }
  }

  .activation-text {
    font-size: 14px;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.5);
  }
}

// 底部结算栏
.checkout-total {
  .total-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }

  .total-price {
    font-size: 20px;
    font-weight: 600;
    color: var(--base-color);
  }
}

.flex-1 {
  flex: 1;
  min-width: 0;
}
