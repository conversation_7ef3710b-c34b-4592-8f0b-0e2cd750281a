<script setup lang="ts">
import { watchEffect } from 'vue'
import { useI18n } from 'vue-i18n'
import { loadVantLocale } from '@/locale/vant'
import Header from '@mobile/components/Header.vue'
const { locale } = useI18n()

watchEffect(() => {
  loadVantLocale(locale)
})
</script>

<template>
  <div class="mobile-layout">
    <!-- 统一Header -->
    <Header />

    <!-- 主要内容区域 -->
    <div class="mobile-content mobile-page-content">
      <RouterView />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mobile-layout {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.mobile-content {
  min-height: 100vh;
}
</style>
