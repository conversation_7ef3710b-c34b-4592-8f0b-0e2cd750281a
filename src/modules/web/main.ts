import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router/index'
import i18n from '@/plugins/i18n'

// 先导入Element Plus相关的样式，然后导入我们的自定义样式
import 'element-plus/dist/index.css'
import setupElementPlus from './plugins/element-plus.ts'
import 'virtual:svg-icons-register'
import '@/assets/main.css' // 确保我们的样式在最后加载，具有最高优先级
import './styles/index.scss'

const app = createApp(App)
import 'virtual:svg-icons-register'

setupElementPlus(app)
app.use(createPinia())
app.use(router)
app.use(i18n)

app.mount('#app')
