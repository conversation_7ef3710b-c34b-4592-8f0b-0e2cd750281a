<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { computed } from 'vue'
import Header from '@web/components/Header.vue'
import { HEADER } from '@/const/mode'

const route = useRoute()
const isHomeView = computed(() => route.name === 'home')
const getHeaderMode = computed(() => {
  return isHomeView.value ? HEADER.TRANSPARENT : HEADER.WHITE
})
</script>

<template>
  <div :class="['layout', isHomeView ? 'top-banner' : '']">
    <Header :show-search="!isHomeView" :mode="getHeaderMode" />
    <main class="main-content">
      <RouterView />
    </main>
  </div>
</template>

<style scoped lang="scss">
.layout {
  background: var(--vt-c-white-soft);
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  // 适配1920px宽度
  @media (min-width: 1920px) {
    max-width: 1920px;
    margin: 0 auto;
  }

  &.top-banner {
    background: #cbcbcb url('../images/BANNER_top_bg.png') center top no-repeat;
  }
}

.main-content {
  flex: 1;
  min-height: 0;
}
</style>
