@use 'sass:color';

.promo-section {
  & + & {
    margin-top: 48px;
  }
}

.section-title {
  font-size: 24px;
  line-height: 1;
  font-weight: 600;
  margin-bottom: 16px;
  color: #000000;
}

.promo-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.promo-card {
  width: calc(50% - 12px);
  min-width: 300px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 20px;
  padding: 25px 24px;
  cursor: pointer;
  position: relative;
  border: 1px solid #e0e0e0;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.selected {
    border: 4px solid var(--base-color);
  }

  &.disabled {
    opacity: 0.7;
    cursor: default;

    &:hover {
      box-shadow: none;
    }

    .promo-value,
    .promo-details,
    .promo-code {
      color: #cccccc;
    }
  }

  .promo-content {
    display: flex;
    position: relative;
    padding-right: 40px;
    width: 100%;
  }

  .best-savings-tag {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--base-color);
    color: white;
    font-size: 20px;
    line-height: 1;
    font-weight: 600;
    padding: 8px 16px;
    border-bottom-left-radius: 20px;
  }
}

.promo-value {
  flex: 1;
  max-width: 196px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #ff2424;

  .percent-value {
    font-size: 32px;
    font-weight: 700;
    line-height: 1;
  }

  .off-text {
    font-size: 24px;
    margin-top: 10px;
  }

  .fixed-amount {
    font-size: 32px;
    font-weight: 700;
  }
}

.promo-details {
  flex: 1;
  height: 100%;
  color: rgba(0, 0, 0, 0.5);
  line-height: 1;
  position: relative;
  padding: 3px 0;
  border-left: 1px dashed #e0e0e0;
  padding-left: 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .promo-label {
    font-size: 20px;
  }

  .promo-code {
    font-size: 24px;
    font-weight: 600;
    color: #000000;
    margin-top: 20px;
  }

  .promo-validity {
    font-size: 20px;
    margin-top: 20px;
  }
}

.promo-condition {
  width: 100%;
  margin-top: 24px;
  font-size: 16px;
  color: #cccccc;
}

.selection-indicator {
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);

  .circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;

    &.checked {
      border-color: var(--base-color);
      background-color: var(--base-color);
    }
  }
}
