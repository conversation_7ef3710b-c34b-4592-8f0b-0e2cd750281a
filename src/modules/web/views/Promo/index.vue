<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Icon } from '@/components/Icon/index'
import FixedBottom from '../../components/FixedBottom.vue'

const router = useRouter()
const { t } = useI18n()

interface PromoCode {
  id: number
  type: 'percent' | 'amount'
  value: number
  code: string
  validUntil: string
  isBestSaving: boolean
  isSelected: boolean
  isDisabled?: boolean
  condition?: string
}

// 可用的优惠码
const availablePromos = ref<PromoCode[]>([
  {
    id: 1,
    type: 'percent',
    value: 50,
    code: 'GCU50',
    validUntil: '04/02/2025',
    isBestSaving: false,
    isSelected: false,
  },
  {
    id: 2,
    type: 'amount',
    value: 25,
    code: 'USD 25',
    validUntil: '04/02/2025',
    isBestSaving: true,
    isSelected: false,
  },
  {
    id: 3,
    type: 'percent',
    value: 60,
    code: 'GCU60',
    validUntil: '04/02/2025',
    isBestSaving: true,
    isSelected: true,
  },
])

// 有条件限制的优惠码
const conditionalPromos = ref<PromoCode[]>([
  {
    id: 4,
    type: 'percent',
    value: 50,
    code: 'GCU50',
    validUntil: '04/02/2025',
    isBestSaving: false,
    isSelected: false,
    isDisabled: true,
    condition: 'Only applicable to data priced over $10',
  },
])

// 选择优惠码
const selectPromo = (promo: PromoCode, section: 'available' | 'conditional') => {
  if (promo.isDisabled) return

  if (section === 'available') {
    availablePromos.value.forEach((p) => (p.isSelected = p.id === promo.id))
    conditionalPromos.value.forEach((p) => (p.isSelected = false))
  } else {
    conditionalPromos.value.forEach((p) => (p.isSelected = p.id === promo.id))
    availablePromos.value.forEach((p) => (p.isSelected = false))
  }
}

// 使用选中的优惠码
const useSelectedPromo = () => {
  const selectedPromo = [...availablePromos.value, ...conditionalPromos.value].find(
    (p) => p.isSelected,
  )
  if (selectedPromo) {
    console.log('使用优惠码:', selectedPromo.code)
    // TODO: 实现优惠码应用逻辑，可能需要跳转到结账页面或调用API
    router.back()
  }
}
</script>

<template>
  <div class="shop-detail container-box">
    <h1 class="shop-title">{{ t('promo.title') }}</h1>

    <!-- 可用优惠码 -->
    <div class="promo-section">
      <h2 class="section-title">
        {{ t('promo.available_promos') }} ({{ availablePromos.length }})
      </h2>

      <div class="promo-cards">
        <div
          v-for="promo in availablePromos"
          :key="promo.id"
          class="promo-card"
          :class="{ selected: promo.isSelected }"
          @click="selectPromo(promo, 'available')"
        >
          <div class="promo-content">
            <!-- 优惠值 -->
            <div class="promo-value">
              <template v-if="promo.type === 'percent'">
                <div class="percent-value">{{ promo.value }}%</div>
                <div class="off-text">off</div>
              </template>
              <template v-else>
                <div class="fixed-amount">{{ promo.code }}</div>
              </template>
            </div>

            <!-- 优惠码详情 -->
            <div class="promo-details">
              <div class="promo-label">
                <template v-if="promo.type === 'percent'">
                  {{ t('promo.promo_code') }}: {{ promo.code }}
                </template>
                <template v-else>{{ t('promo.coupon') }}</template>
              </div>
              <div class="promo-code">No-Minimum</div>
              <div class="promo-validity">{{ t('promo.valid_until') }} {{ promo.validUntil }}</div>
            </div>
          </div>

          <!-- 最佳优惠标签 -->
          <div v-if="promo.isBestSaving" class="best-savings-tag">
            {{ t('promo.best_savings') }}
          </div>

          <!-- 选择指示器 -->
          <div class="selection-indicator">
            <div class="circle" :class="{ checked: promo.isSelected }">
              <Icon v-if="promo.isSelected" icon="svg-icon:complete" size="32" color="#fff"></Icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 有条件限制的优惠码 -->
    <div class="promo-section" v-if="conditionalPromos.length > 0">
      <h2 class="section-title">
        {{ t('promo.unavailable_promos') }} ({{ conditionalPromos.length }})
      </h2>

      <div class="promo-cards">
        <div
          v-for="promo in conditionalPromos"
          :key="promo.id"
          class="promo-card disabled"
          :class="{ selected: promo.isSelected }"
          @click="selectPromo(promo, 'conditional')"
        >
          <div class="promo-content">
            <!-- 优惠值 -->
            <div class="promo-value">
              <template v-if="promo.type === 'percent'">
                <div class="percent-value">{{ promo.value }}%</div>
                <div class="off-text">off</div>
              </template>
              <template v-else>
                <div class="fixed-amount">{{ promo.code }}</div>
              </template>
            </div>

            <!-- 优惠码详情 -->
            <div class="promo-details">
              <div class="promo-label">
                <template v-if="promo.type === 'percent'">
                  {{ t('promo.promo_code') }}: {{ promo.code }}
                </template>
                <template v-else>{{ t('promo.coupon') }}</template>
              </div>
              <div class="promo-code">Minimum spending of USD 100</div>
              <div class="promo-validity">{{ t('promo.valid_until') }} {{ promo.validUntil }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用优惠码按钮 -->
    <FixedBottom :text="t('promo.use_promo')" @click-button="useSelectedPromo"></FixedBottom>
  </div>
</template>

<style src="../../styles/shop-common.scss" scoped lang="scss"></style>
<style src="./Promo.scss" scoped lang="scss"></style>
