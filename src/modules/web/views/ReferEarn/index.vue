<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import RewardItem from './components/RewardItem.vue'
import Pagination from '@/components/Pagination/index.vue'
import DownloadApp from '@web/views/Home/DownloadApp.vue'
import Copyright from '@web/components/Copyright.vue'
import Footer from '@web/components/Footer.vue'
import EsimEmpty from '@web/components/EsimEmpty.vue'

const { t } = useI18n()

// 引用代码
const referralCode = ref('CIEL1234')

// 复制引用代码
const copyReferralCode = () => {
  navigator.clipboard
    .writeText(referralCode.value)
    .then(() => {
      ElMessage.success(t('refer.copy_success'))
    })
    .catch(() => {
      ElMessage.error(t('refer.copy_failed'))
    })
}

// 分享功能
const shareNow = () => {
  // 这里可以实现社交媒体分享逻辑
  // 例如打开分享弹窗或直接分享到特定平台
  if (navigator.share) {
    navigator
      .share({
        title: t('refer.share_title'),
        text: t('refer.share_text', { code: referralCode.value }),
        url: window.location.href,
      })
      .catch(() => {
        ElMessage.info(t('refer.share_manually'))
      })
  } else {
    copyReferralCode()
    ElMessage.info(t('refer.share_manually'))
  }
}

// 奖励历史数据
const rewardHistory = reactive({
  total: 12,
  currentPage: 1,
  pageSize: 5,
})

const rewardList = reactive([
  { id: 1, type: 'invitation', date: '2023/01/01' },
  { id: 2, type: 'invitation', date: '2023/01/01' },
  { id: 3, type: 'invitation', date: '2023/01/01' },
  { id: 4, type: 'invitation', date: '2023/01/01' },
  { id: 5, type: 'invitation', date: '2023/01/01' },
])

// 分页变化处理
const handlePageChange = (page: number) => {
  rewardHistory.currentPage = page
  // 这里可以添加获取对应页数据的逻辑
}
</script>

<template>
  <div class="refer-earn-container">
    <h1 class="refer-earn-title">{{ t('refer.title') }}</h1>

    <div class="container-box refer-earn-content">
      <!-- 左侧图片 -->
      <div class="refer-image">
        <img src="../../images/refer.png" alt="Refer and Earn" />
      </div>

      <!-- 右侧内容 -->
      <div class="refer-info">
        <div class="refer-content">
          <h2 class="refer-reward-title">
            {{ t('refer.reward_title') }}
          </h2>

          <h3 class="refer-subtitle">
            {{ t('refer.subtitle') }}
          </h3>

          <p class="refer-description">
            {{ t('refer.description') }}
          </p>

          <!-- 引用代码部分 -->
          <div class="referral-code-section">
            <p class="referral-label">{{ t('refer.your_code') }}</p>
            <span class="referral-code">{{ referralCode }}</span>
            <el-button class="copy-button" round @click="copyReferralCode">{{
              t('refer.copy')
            }}</el-button>
          </div>

          <!-- 分享按钮 -->
          <el-button type="primary" class="block-button" @click="shareNow">
            {{ t('refer.share_now') }}
          </el-button>
        </div>

        <!-- 奖励历史部分 -->
        <div class="reward-history-section">
          <h2 class="reward-history-title">
            {{ t('refer.reward_history') }}
          </h2>

          <template v-if="rewardList.length > 0">
            <div class="reward-list">
              <RewardItem
                v-for="item in rewardList"
                :key="item.id"
                :type="item.type"
                :date="item.date"
              />
            </div>

            <!-- 分页组件 -->
            <div class="pagination-container">
              <Pagination
                :current-page="rewardHistory.currentPage"
                :per-page-count="rewardHistory.pageSize"
                :total-count="rewardHistory.total"
                @page-change="handlePageChange"
              />
            </div>
          </template>
          <template v-else>
            <EsimEmpty />
          </template>
        </div>
      </div>
    </div>
  </div>
  <DownloadApp />
  <Footer />
  <Copyright />
</template>

<style lang="scss" scoped src="./ReferEarn.scss"></style>
<style lang="scss" scoped>
.reward-history-section {
  margin-top: 100px;
}

.reward-history-title {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}
</style>
