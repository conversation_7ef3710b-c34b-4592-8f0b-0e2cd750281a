.refer-earn-container {
  padding-bottom: 160px;
}

.refer-earn-title {
  font-size: 36px;
  line-height: 1;
  font-weight: 700;
  text-align: center;
  padding-top: 48px;
  padding-bottom: 48px;
  margin-bottom: 32px;
  color: #000;
}

.refer-earn-content {
  display: flex;
  gap: 60px;

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.refer-image {
  flex: 1;

  img {
    max-width: 100%;
    display: block;
    margin: auto;
  }
}

.refer-info {
  flex: 1;
}

.refer-reward-title {
  font-size: 36px;
  line-height: 1;
  font-weight: 700;
  color: #00c65e;
  margin-bottom: 24px;
}

.refer-subtitle {
  font-size: 28px;
  line-height: 1;
  font-weight: 600;
  margin-bottom: 24px;
  color: #000000;
}

.refer-description {
  font-size: 20px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 40px;
}

.referral-code-section {
  padding: 24px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  margin-bottom: 40px;
  position: relative;
}

.referral-label {
  font-size: 20px;
  line-height: 28px;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 16px;
}

.referral-code {
  font-size: 36px;
  line-height: 1;
  font-weight: 700;
  color: #000000;
}

.block-button {
  width: 100%;
}

.copy-button {
  --el-border-color: #000;

  height: auto;
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
}
