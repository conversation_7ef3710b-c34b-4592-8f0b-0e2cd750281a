<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

defineProps({
  type: {
    type: String,
    required: true,
  },
  date: {
    type: String,
    required: true,
  },
})
</script>

<template>
  <div class="reward-item">
    <div class="reward-info">
      <h5 class="reward-type">
        {{ t(`refer.${type}_rewards`) }}
      </h5>
      <p class="reward-date">{{ date }}</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.reward-item {
  padding: 24px;
  border-radius: 20px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.reward-type {
  font-size: 20px;
  font-weight: 500;
  color: #000000;
  margin-bottom: 16px;
}

.reward-date {
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.5);
}
</style>
