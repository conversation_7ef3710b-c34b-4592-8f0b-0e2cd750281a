<script setup lang="ts">
import { ref, computed } from 'vue'
const MODAL_VISIBLE_CODE = 'modal_visible_code'

// 表单数据
const formData = ref({
  code: '',
})

// 表单验证规则
const rules = {
  code: [{ required: true, message: 'Please enter your code', trigger: 'blur' }],
}

// 表单引用
const formRef = ref()

// 记录操作时间
const visible_time = window.localStorage.getItem(MODAL_VISIBLE_CODE)
const modalVisible = ref(visible_time ? false : true)

// 关闭弹窗
const closeModal = () => {
  modalVisible.value = false
  window.localStorage.setItem(MODAL_VISIBLE_CODE, new Date().getTime() + '')

  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 保存
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    console.log('ddd ==> save', formData.value.code)
    closeModal()
  } catch (error) {
    console.log('Validation failed:', error)
  }
}

// 取消
const handleCancel = () => {
  closeModal()
}
</script>

<template>
  <el-dialog
    v-model="modalVisible"
    class="promo-code-modal"
    title="Have a promo/referral code?"
    :show-close="false"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeModal"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" class="modal-form">
      <el-form-item prop="code">
        <el-input v-model="formData.code" placeholder="Enter Code" size="large" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button class="cancel-btn" @click="handleCancel"> No,thanks </el-button>
        <el-button type="primary" class="save-btn" :disabled="!formData.code" @click="handleSave">
          Claim
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style>
.promo-code-modal {
  border-radius: 20px;
  background: var(--vt-c-white-soft);

  .el-dialog__header {
    text-align: left;
    padding: 60px 0 0;

    .el-dialog__title {
      font-size: 36px;
      font-weight: 600;
      color: var(--primary-color);
    }
  }

  .el-dialog__body {
    padding: 60px 0;
  }

  .el-dialog__footer {
    padding: 0 0 32px;
  }
}
</style>

<style scoped lang="scss">
.modal-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cancel-btn {
  padding: 0;
  border: none;
  background: transparent;
  color: var(--primary-color);
  font-size: 20px;
  font-weight: 500;

  &:hover {
    color: var(--primary-color);
    background: transparent;
  }
}

.save-btn {
  width: 280px;
  border-radius: 36px;
  font-size: 24px;
  font-weight: 600;
}
</style>
