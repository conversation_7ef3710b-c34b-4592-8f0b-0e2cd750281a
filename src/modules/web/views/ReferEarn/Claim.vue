<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import DownloadApp from '@web/views/Home/DownloadApp.vue'
import Copyright from '@web/components/Copyright.vue'
import Footer from '@web/components/Footer.vue'

const { t } = useI18n()
const router = useRouter()

// 促销代码
const promoCode = ref('CIEL1234')

// 复制促销代码
const copyPromoCode = () => {
  navigator.clipboard
    .writeText(promoCode.value)
    .then(() => {
      ElMessage.success(t('claim.copy_success'))
    })
    .catch(() => {
      ElMessage.error(t('claim.copy_failed'))
    })
}

// 注册并领取
const signUpAndClaim = () => {
  // 这里可以实现跳转到注册页面的逻辑
  router.push({ name: 'register', query: { promo: promoCode.value } })
}
</script>

<template>
  <div class="refer-earn-container">
    <h1 class="refer-earn-title">{{ t('claim.title') }}</h1>

    <div class="container-box refer-earn-content">
      <!-- 左侧图片 -->
      <div class="refer-image">
        <img src="../../images/refer.png" alt="Claim Illustration" />
      </div>

      <!-- 右侧内容 -->
      <div class="refer-info">
        <div class="refer-content">
          <h2 class="refer-reward-title claim-title">
            {{ t('claim.reward_title') }}
          </h2>

          <h3 class="refer-subtitle claim-subtitle">
            {{ t('claim.subtitle') }}
          </h3>

          <!-- 促销代码部分 -->
          <div class="referral-code-section">
            <p class="referral-label">{{ t('claim.code_label') }}</p>
            <span class="referral-code">{{ promoCode }}</span>
            <el-button class="copy-button" round @click="copyPromoCode">{{
              t('claim.copy')
            }}</el-button>
          </div>

          <!-- 注册按钮 -->
          <el-button type="primary" class="block-button" @click="signUpAndClaim">
            {{ t('claim.sign_up') }}
          </el-button>

          <!-- 使用说明 -->
          <div class="claim-guide">
            <h4 class="guide-title">{{ t('claim.how_to_use') }}</h4>
            <ol class="guide-steps">
              <li>{{ t('claim.step1') }}</li>
              <li>{{ t('claim.step2') }}</li>
              <li>{{ t('claim.step3') }}</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  </div>
  <DownloadApp />
  <Footer />
  <Copyright />
</template>

<style lang="scss" scoped src="./ReferEarn.scss"></style>
<style lang="scss" scoped>
.claim-title {
  margin-bottom: 36px;
}

.claim-subtitle {
  margin-bottom: 40px;
}

.claim-guide {
  margin-top: 48px;
}

.guide-title {
  font-size: 24px;
  line-height: 1;
  font-weight: 600;
  margin-bottom: 24px;
}

.guide-steps {
  li {
    line-height: 36px;
    font-size: 20px;
  }
}
</style>
