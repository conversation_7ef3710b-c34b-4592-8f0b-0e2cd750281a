@use 'sass:color';

// 产品信息样式
.checkout-product {
  padding: 20px;
  height: 80px;
  background: #ffffff;
  border-radius: 20px 20px 20px 20px;
  border: 1px solid #e0e0e0;
  margin-bottom: 48px;
}

.product-name {
  font-size: 20px;
  line-height: 2;
  font-weight: 600;
  color: #333;
}

.checkout-summary {
  display: flex;
}

.summary-activation,
.summary-info {
  flex: 1;
  min-width: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .section-title {
    font-size: 24px;
    font-weight: 600;
    color: #000000;
  }

  .section-link {
    color: var(--base-color);
    text-decoration: none;
    font-size: 20px;
    text-decoration: underline;

    &:hover {
      color: #00c853;
    }
  }
}

// 激活信息样式
.summary-activation {
  .activation-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .info-icon {
    margin-right: 15px;
  }

  .info-title {
    font-size: 24px;
    line-height: 1;
    color: #000000;
    font-weight: 600;
  }

  .activation-text {
    font-size: 20px;
    line-height: 30px;
    color: #000000;
  }
}

// 优惠码区域样式
.promo-section {
  .promo-input-group {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    height: 80px;
    background: #ffffff;
    border-radius: 20px;
    border: 1px solid #e0e0e0;

    .promo-icon {
      margin-right: 24px;
      padding-right: 24px;
      border-right: 1px solid #e0e0e0;
    }

    .promo-input {
      flex: 1;
      border: none;
      height: 100%;
      font-size: 20px;
      line-height: 1;

      &:focus {
        outline: none;
        border-color: var(--base-color);
      }
    }

    .apply-button {
      background-color: #fff;
      border: 1px solid var(--base-color);
      color: var(--base-color);
      padding: 10px 20px;
      border-radius: 20px;
      font-size: 20px;
      line-height: 1;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(0, 200, 83, 0.1);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .onsale {
      font-size: 24px;
      line-height: 36px;
      color: #ff2424;
      margin-right: 90px;
      &-percent {
        font-size: 36px;
        padding-right: 10px;
      }
    }
    .close-icon {
      cursor: pointer;
    }
  }
}

// 订单摘要样式
.order-summary {
  margin-top: 48px;

  .order-group {
    padding: 0 24px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    background-color: #ffffff;
    .group-cell {
      padding: 32px 0;
      & + .group-cell {
        border-top: 1px solid #e0e0e0;
      }
    }
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    color: #000000;
    font-size: 20px;
    & + .summary-item {
      margin-top: 32px;
    }

    .item-value {
      font-weight: 600;
      &.discount-value {
        color: #ff2424;
      }
    }

    .total-value {
      font-size: 32px;
      font-weight: bold;
    }
  }
}
