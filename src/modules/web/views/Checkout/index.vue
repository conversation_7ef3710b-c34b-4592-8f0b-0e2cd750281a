<script lang="ts" setup>
import { Icon } from '@/components/Icon/index'
import FixedBottom from '../../components/FixedBottom.vue'
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const router = useRouter()
const { t } = useI18n()

// 订单信息
const orderInfo = ref({
  productName: '[eSIM] 5GB 3-Day Package for United States',
  activationPeriod: '30-Day',
  activationDeadline: 'April 26',
  promoCode: 'GCU50',
  subtotal: 10.0,
  discount: 0.5,
  total: 9.5,
  country: 'United States',
  plan: '5GB / 30 Days',
})

// 是否应用了优惠码
const isPromoApplied = ref(false)

// 计算最终价格
const finalTotal = computed(() => {
  return orderInfo.value.subtotal - (isPromoApplied.value ? orderInfo.value.discount : 0)
})

// 应用优惠码
const applyPromo = () => {
  isPromoApplied.value = true
}
// 清除优惠码
const clearPromo = () => {
  isPromoApplied.value = false
  orderInfo.value.promoCode = ''
}

// 生成订单
const handleClick = () => {
  router.push({ name: 'payment' })
}
</script>

<template>
  <div class="shop-detail">
    <div class="container-box">
      <h1 class="shop-title">{{ t('checkout.title') }}</h1>

      <!-- 产品信息 -->
      <div class="checkout-product">
        <div class="product-name">{{ orderInfo.productName }}</div>
      </div>
      <div class="checkout-summary">
        <!-- 激活信息 -->
        <div class="summary-activation">
          <div class="activation-info">
            <Icon class="info-icon" icon="svg-icon:question" size="24"></Icon>
            <h3 class="info-title">{{ t('checkout.activate_later') }}</h3>
          </div>
          <p class="activation-text">
            {{
              t('checkout.activation_period_text', {
                period: orderInfo.activationPeriod,
                deadline: orderInfo.activationDeadline,
              })
            }}
          </p>
        </div>

        <div class="summary-info">
          <!-- 优惠码区域 -->
          <div class="promo-section">
            <div class="section-header">
              <h3 class="section-title">{{ t('checkout.promo') }}</h3>
              <RouterLink :to="{ name: 'checkout-promo' }" class="section-link">{{
                t('checkout.my_promo')
              }}</RouterLink>
            </div>

            <div class="promo-input-group">
              <div class="promo-icon">
                <Icon icon="svg-icon:promo" size="36" color="#333333"></Icon>
              </div>
              <input
                type="text"
                class="promo-input"
                v-model="orderInfo.promoCode"
                :placeholder="t('promo.promo_code')"
              />
              <template v-if="isPromoApplied">
                <span class="onsale"><span class="onsale-percent">50%</span>off</span>
                <Icon class="close-icon" @click="clearPromo" icon="svg-icon:close" size="24"></Icon>
              </template>
              <template v-else>
                <button class="apply-button" @click="applyPromo">
                  {{ t('checkout.apply') }}
                </button>
              </template>
            </div>
          </div>

          <!-- 订单摘要 -->
          <div class="order-summary">
            <div class="section-header">
              <h3 class="section-title">{{ t('checkout.order_summary') }}</h3>
            </div>
            <div class="order-group">
              <div class="group-cell summary-item">
                <span class="item-label">1 {{ t('checkout.item') }}</span>
              </div>

              <div class="group-cell">
                <div class="summary-item">
                  <span class="item-label">{{ t('checkout.subtotal') }}</span>
                  <span class="item-value">USD {{ orderInfo.subtotal.toFixed(2) }}</span>
                </div>
                <div class="summary-item" v-if="isPromoApplied">
                  <span class="item-label">{{ t('checkout.vip_discount') }}</span>
                  <span class="item-value discount-value"
                    >-USD {{ orderInfo.discount.toFixed(1) }}</span
                  >
                </div>
              </div>

              <div class="group-cell summary-item">
                <span class="item-label">{{ t('checkout.total') }}</span>
                <span class="item-value"
                  >USD <span class="total-value">{{ finalTotal.toFixed(2) }}</span></span
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 底部结算栏 -->
        <FixedBottom :text="t('checkout.place_order')" @click-button="handleClick">
          <div class="selected">
            <div class="selected-plan">
              <span class="selected-plan-country">{{ orderInfo.country }}</span>
              <div class="selected-plan-info">{{ orderInfo.plan }}</div>
            </div>

            <div class="selected-total">
              <span class="selected-total-label">{{ t('checkout.total') }}</span>
              <div class="selected-total-value">
                <span class="currency">USD</span>{{ finalTotal.toFixed(2) }}
              </div>
            </div>
          </div>
        </FixedBottom>
      </div>
    </div>
  </div>
</template>

<style src="../../styles/shop-common.scss" scoped lang="scss"></style>
<style src="./Checkout.scss" scoped lang="scss"></style>
