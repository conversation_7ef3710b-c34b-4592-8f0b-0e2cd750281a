<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { LINKS_DOWNLOAD as downloadLinks } from '@/const/links'
import { getImageUrl } from '@/utils/getImageUrl'
const { t } = useI18n()
</script>

<template>
  <section class="download-app">
    <div class="download-app__inner">
      <div class="container-box">
        <div class="content-wrapper">
          <div class="text-content">
            <h2 class="section-title">{{ t('home.download_app.title') }}</h2>
            <p class="section-description">
              {{ t('home.download_app.description') }}
            </p>
            <div class="app-buttons">
              <a
                v-for="item in downloadLinks"
                :key="item.name"
                :href="item.url"
                class="app-button"
                :title="t(item.name)"
              >
                <img :src="getImageUrl(`${item.icon}.svg`)" alt="" />
              </a>
            </div>
          </div>
          <div class="phone-mockup">
            <div class="phone-mockup__inner">
              <img src="../../images/BANNER_download_phone.png" alt="GlocalMe App Screenshot" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped src="./DownloadApp.scss"></style>
