.user-reviews {
  width: 100%;
  padding: 160px 0;
  background-color: var(--vt-c-white-soft);
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 60px;
  color: #000000;
}

.reviews-content {
  display: flex;
  gap: 90px;

  @media (max-width: 992px) {
    flex-direction: column;
  }
}

// 左侧平台评分样式
.platform-ratings {
  flex: 0 0 340px;

  @media (max-width: 992px) {
    margin-bottom: 40px;
  }
}

.platform-rating {
  display: flex;
  align-items: center;
  margin-bottom: 80px;

  &:last-child {
    margin-bottom: 0;
  }
}

.platform-logo {
  flex: 0 0 98px;
  height: 98px;
  margin-right: 20px;
}

.rating-info {
  flex: 1;
}

.rating-score {
  font-size: 55px;
  font-weight: 700;
  color: #000000;
  line-height: 1;
  margin-bottom: 12px;

  .rating-max {
    font-size: 32px;
    font-weight: 400;
  }
}

.rating-stars {
  display: flex;
  gap: 8px;
}

.star {
  font-size: 36px;
  line-height: 1;
  color: #ddd;

  &.filled {
    color: #ffd700;
  }
}

// 右侧评价轮播样式
.reviews-carousel {
  flex: 1;
  min-width: 0;
}
