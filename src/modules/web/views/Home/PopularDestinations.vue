<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import EsimShare from '@/components/EsimShare/index.vue'
import EsimDestination from '@web/components/EsimDestination.vue'
import { useRequest } from 'vue-hooks-plus'
import { getPopularDestinations, DestinationKeyword } from '@/api'
import type { PopularDestination } from '@/api/Country/types'
import { DestinationType, destinationTypes } from '@/data/destinations'
import { paramsWrap } from '@/api/utils'

const { t } = useI18n()
const activeTab = ref(DestinationType.LOCAL)

const destinations = ref<PopularDestination[]>([])
const { loading } = useRequest(() => getPopularDestinations(paramsWrap({ type: DestinationKeyword.HOT_ESIM_KEYWORD })),
  {
    staleTime: 50000,
    onSuccess: data => {
      destinations.value = data?.data || []
    }
  }
)
const list = computed(() => {
  let temp = destinations.value
  if (activeTab.value === DestinationType.REGION) {
    temp = temp.filter(d => (d.keyType || '').toLocaleLowerCase() === DestinationType.REGION)
  }
  return temp.slice(0, 16)
})

const router = useRouter()
// 处理查看更多
const handleShowMore = () => {
  router.push({ name: 'shop-plans' })
}
// 点击套餐信息
const handleClickCell = (item) => {
  router.push({
    name: 'plan-detail',
    params: {
      pid: item.iso2,
    },
    query: {
      name: item.value,
    },
  })
}
</script>

<template>
  <section class="popular-destinations">
    <div class="container-box">
      <h2 class="section-title">{{ t('home.popular.title') }}</h2>

      <!-- 使用Element Plus的Tabs组件实现标签切换 -->
      <div class="tabs-container">
        <el-tabs v-model="activeTab" class="custom-tabs">
          <el-tab-pane v-for="item in destinationTypes" :name="item.value" :label="t(item.label)"></el-tab-pane>
        </el-tabs>
      </div>

      <!-- 目的地网格 -->
      <EsimDestination v-loading="loading" :list="list" @click-cell="handleClickCell"></EsimDestination>

      <!-- 显示更多按钮 -->
      <div class="show-more-container">
        <EsimShare @click="handleShowMore">Show More</EsimShare>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped src="./PopularDestinations.scss"></style>
