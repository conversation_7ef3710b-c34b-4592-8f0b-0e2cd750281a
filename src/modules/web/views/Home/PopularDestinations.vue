<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import EsimShare from '@/components/EsimShare/index.vue'
import EsimDestination from '@web/components/EsimDestination.vue'
import { destinationTypes } from '@/data/destinations'
import { useDestinations, useDestinationNavigation } from '@/hooks/useDestinations'

const { t } = useI18n()

// 使用目的地数据 hook
const {
  activeTab,
  loading,
  limitedDestinations: list,
  setActiveTab
} = useDestinations()

// 使用目的地导航 hook
const { goToPlanDetail, goToShopPlans } = useDestinationNavigation()

// 处理查看更多
const handleShowMore = () => {
  goToShopPlans()
}

// 点击套餐信息
const handleClickCell = (item: any) => {
  goToPlanDetail(item)
}
</script>

<template>
  <section class="popular-destinations">
    <div class="container-box">
      <h2 class="section-title">{{ t('home.popular.title') }}</h2>

      <!-- 使用Element Plus的Tabs组件实现标签切换 -->
      <div class="tabs-container">
        <el-tabs :model-value="activeTab" @update:model-value="setActiveTab" class="custom-tabs">
          <el-tab-pane v-for="item in destinationTypes" :name="item.value" :label="t(item.label)"></el-tab-pane>
        </el-tabs>
      </div>

      <!-- 目的地网格 -->
      <EsimDestination v-loading="loading" :list="list" @click-cell="handleClickCell"></EsimDestination>

      <!-- 显示更多按钮 -->
      <div class="show-more-container">
        <EsimShare @click="handleShowMore">Show More</EsimShare>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped src="./PopularDestinations.scss"></style>
