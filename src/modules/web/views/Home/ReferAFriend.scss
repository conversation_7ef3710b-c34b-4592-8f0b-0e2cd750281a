.refer-a-friend {
  position: relative;
  padding-top: 250px;
  padding-bottom: 90px;
  background-color: var(--vt-c-white-soft);

  .refer-inner {
    position: relative;
    display: flex;
    padding: 80px 20px;
    border-radius: 20px;
    background: url('../../images/BANNER_share_bg.png') center 100% no-repeat;
    background-size: contain;
  }

  .refer-img {
    flex: 2;
    img {
      position: absolute;
      bottom: 0;
    }
  }

  .refer-content {
    flex: 1;
  }

  .refer-txt {
    color: #000000;
    font-size: 32px;
    line-height: 38px;
    margin-bottom: 24px;

    &__highlight {
      font-weight: bold;
      display: block;
    }
  }
}
