.how-it-works {
  width: 100%;
  padding: 120px 0;
  background-color: #ffffff;
}

.section-title {
  font-size: 36px;
  line-height: 1;
  font-weight: 700;
  text-align: center;
  margin-bottom: 96px;
  color: #000000;
}

.workflow-steps {
  display: flex;
  justify-content: space-between;
  gap: 20px;

  @media (max-width: 992px) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

.workflow-step {
  flex: 1;
  max-width: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  @media (max-width: 992px) {
    flex: 0 0 calc(50% - 10px);
    margin-bottom: 40px;
  }

  @media (max-width: 576px) {
    flex: 0 0 100%;
  }
}

.phone-container {
  // width: 100%;
  // height: 320px;
  width: 220px;
  height: 440px;
  background: #d7e1e6;
  border-radius: 20px;
  text-align: center;
  margin-bottom: 60px;
}

.phone-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.1));
}

.step-indicator {
  padding: 2px 5px;
  background-color: #d9d9d9;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.step-number {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.step-title {
  font-size: 20px;
  line-height: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 28px;
}

.step-description {
  font-size: 16px;
  line-height: 24px;
  color: #666;
  text-align: center;
}
