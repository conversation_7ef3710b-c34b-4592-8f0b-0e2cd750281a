<script lang="ts" setup>
import { ref } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'

import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'

import { Pagination, Navigation } from 'swiper/modules'

defineProps({
  list: {
    type: Array<any>,
    default: () => [],
  },
})

// 生成星级评分
const generateStars = (rating: number) => {
  return Array.from({ length: 5 }, (_, i) => i < rating)
}
const modules = ref([Pagination, Navigation])
</script>

<template>
  <swiper
    :slidesPerView="'auto'"
    navigation
    :space-between="50"
    :pagination="{
      clickable: true,
    }"
    :modules="modules"
  >
    <swiper-slide v-for="item in list" :key="item.id">
      <div class="review-card">
        <div class="review-card__inner">
          <div class="card-media">
            <div class="review-header">
              <div class="user-avatar">
                <img :src="item.avatar" :alt="item.name" />
              </div>
              <div class="user-info">
                <div class="rating-stars">
                  <i
                    v-for="(isFilled, index) in generateStars(item.rating)"
                    :key="index"
                    class="star"
                    :class="{ filled: isFilled }"
                    >★</i
                  >
                </div>
                <h3 class="review-title">{{ item.title }}</h3>
                <p class="review-date">{{ item.date }} - {{ item.name }}</p>
              </div>
            </div>
            <div class="review-content">
              <p>{{ item.content }}</p>
            </div>
          </div>
          <div class="card-pic">
            <div class="placehold"></div>
          </div>
        </div>
      </div>
    </swiper-slide>
  </swiper>
</template>

<style lang="scss" scoped>
.swiper {
  --swiper-navigation-size: 14px;
  --swiper-theme-color: #666666;
  --swiper-pagination-bullet-inactive-color: #d9d9d9;

  &-slide {
    width: 830px;
    height: 468px;
  }

  :deep(.swiper-pagination) {
    width: 830px;
  }

  :deep(.swiper-button-next) {
    right: auto;
    left: 840px;
  }

  :deep(.swiper-button-prev),
  :deep(.swiper-button-next) {
    width: 34px;
    height: 34px;
    color: #ffffff;
    background: #adadad;
    border-radius: 50%;

    &.swiper-button-disabled {
      display: none;
    }
  }
}

.review-card {
  background-color: #fff;
  border-radius: 16px;
  padding: 100px 60px 100px;
  height: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  &__inner {
    display: flex;
    gap: 60px;
  }
}

.card-media {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-pic {
  width: 262px;
  height: 246px;
  background-color: var(--vt-c-white-soft);
  border-radius: 100px 100px 0px 0px;
}

.review-header {
  display: flex;
  margin-bottom: 16px;
}

.user-avatar {
  width: 98px;
  height: 98px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24px;
  background-color: #f0f0f0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.user-info {
  flex: 1;

  .rating-stars {
    margin-bottom: 8px;

    .star {
      font-size: 24px;
      line-height: 1;
    }
  }
}

.review-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px;
  color: #000;
}

.review-date {
  font-size: 16px;
  color: #999999;
  margin: 0;
}

.review-content {
  flex: 1;
  font-size: 16px;
  line-height: 24px;
  color: #666666;

  p {
    margin: 0;
  }
}
</style>
