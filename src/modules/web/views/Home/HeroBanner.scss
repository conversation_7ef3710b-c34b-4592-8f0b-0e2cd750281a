.hero-banner {
  width: 100%;
  position: relative;
}

.hero-container {
  &-inner {
    min-height: 560px;
    display: flex;
    position: relative;
  }
}

.hero-image {
  margin-top: -70px;
  flex: 1;
  min-width: 0;
  height: 100%;
  position: relative;
  text-align: center;

  img {
    height: 100%;
    object-fit: cover;
    position: relative;
    z-index: 2;
  }
}

.hero-content {
  flex: 1;
  min-width: 0;
  height: 100%;
  &-inner {
    padding-right: 80px;
    padding-left: 120px;
    padding-top: 70px;
    padding-bottom: 175px;
  }

  .hero-title {
    font-size: 64px;
    line-height: 72px;
    font-weight: 700;
    margin-bottom: 30px;
    color: #000;
  }

  .hero-highlight {
    color: #156326;
    display: block;
  }

  .search-box {
    position: relative;

    input {
      width: 100%;
      padding: 20px 17px 20px 63px;
      border-radius: 100px;
      border: 1px solid #d9d9d9;
      font-size: 24px;

      &::placeholder {
        color: #999;
      }
    }

    .search-icon {
      position: absolute;
      left: 17px;
      top: 50%;
      transform: translateY(-50%);
      svg {
        display: block;
      }
    }
  }
}

.promo-container {
  display: flex;
  gap: 20px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: -40px;
  z-index: 2;
}

.promo-box {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 36px;
  border-radius: 10px;
  background-color: #0c3d18;
  color: white;

  .promo-text {
    font-size: 24px;
    font-weight: 500;
    word-break: break-all;
  }

  .promo-adventure {
    cursor: pointer;
    font-size: 20px;
    line-height: 1;
    padding: 14px 26px;
    border-radius: 12px;
    background-color: #156326;
    color: #4dd65d;
    font-weight: 600;
    width: 148px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-wrap: nowrap;
  }
}
