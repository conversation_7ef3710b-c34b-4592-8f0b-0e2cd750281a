<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import UserCarousel from './UserCarousel.vue'
import { Icon } from '@/components/Icon/index'
import { ref } from 'vue'

const { t } = useI18n()
// 用户评价数据
const reviews = ref([
  {
    id: 1,
    avatar: '/src/assets/images/avatars/user1.jpg',
    name: '<PERSON>',
    date: '2023年12月21日',
    rating: 5,
    title: 'Great deadzone',
    content:
      'No need to get up and buy a sim card for the place your traveling to,just buy in app and enjoy the freedom of that worry.Its really easy and wonderful experience, and price is as good as can be plus they offer really cool coupons which came in handy!!!',
  },
  {
    id: 2,
    avatar: '/src/assets/images/avatars/user2.jpg',
    name: '<PERSON>',
    date: '2023年12月15日',
    rating: 5,
    title: 'Great deadzone',
    content:
      'No need to get up and buy a sim card for the place your traveling to,just buy in app and enjoy the freedom of that worry.Its really easy and wonderful experience, and price is as good as can be plus they offer really cool coupons which came in handy!!!',
  },
  {
    id: 3,
    avatar: '/src/assets/images/avatars/user3.jpg',
    name: '<PERSON>',
    date: '2023年11月30日',
    rating: 5,
    title: 'Excellent service',
    content:
      'This app saved me so much time and hassle during my international trip. The connection was stable and fast everywhere I went. Highly recommend to all travelers!',
  },
  {
    id: 4,
    avatar: '/src/assets/images/avatars/user4.jpg',
    name: 'Emma Wilson',
    date: '2023年11月25日',
    rating: 4,
    title: 'Very convenient',
    content:
      'I love how easy it is to get connected in a new country. The interface is intuitive and the prices are reasonable. Would definitely use again on my next trip.',
  },
])

// 平台评分数据
const platforms = [
  {
    name: 'App Store',
    logo: 'appstore',
    rating: 4.7,
  },
  {
    name: 'Google Play',
    logo: 'playstore',
    rating: 4.7,
  },
  {
    name: 'Trustpilot',
    logo: 'trustpilot',
    rating: 4.7,
  },
]

// 生成星级评分
const generateStars = (rating: number) => {
  return Array.from({ length: 5 }, (_, i) => i < rating)
}
</script>

<template>
  <section class="user-reviews">
    <div class="container-box">
      <h2 class="section-title">{{ t('home.user_reviews.title') }}</h2>

      <div class="reviews-content">
        <!-- 左侧平台评分 -->
        <div class="platform-ratings">
          <div v-for="platform in platforms" :key="platform.name" class="platform-rating">
            <div class="platform-logo">
              <Icon :icon="`svg-icon:${platform.logo}`" size="98"></Icon>
            </div>
            <div class="rating-info">
              <div class="rating-score">
                {{ platform.rating }}<span class="rating-max">/5</span>
              </div>
              <div class="rating-stars">
                <i
                  v-for="(isFilled, index) in generateStars(5)"
                  :key="index"
                  class="star"
                  :class="{ filled: isFilled }"
                  >★</i
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧用户评价轮播 -->
        <div class="reviews-carousel">
          <UserCarousel :list="reviews" />
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped src="./UserReviews.scss"></style>
