<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import EsimShare from '@/components/EsimShare/index.vue'

const { t } = useI18n()

const handleLearnMore = () => {
  // 处理"Learn More"按钮点击事件
  console.log('Learn More clicked')
  // 可以添加导航到详情页面的逻辑
  // router.push('/package-details');
}
</script>

<template>
  <section class="hero-exclusive">
    <div class="container-box">
      <div class="exclusive-content">
        <div class="exclusive-tag">{{ t('home.exclusive.tag') }}</div>
        <h1 class="exclusive-title">
          <span class="highlight">{{ t('home.exclusive.title') }}</span>
          <span class="subtitle">{{ t('home.exclusive.subtitle') }}</span>
        </h1>

        <EsimShare @click="handleLearnMore" mode="plain">
          <span class="fw-bold">{{ t('home.exclusive.button') }}</span>
        </EsimShare>
      </div>
    </div>
  </section>
</template>

<style src="./Exclusive.scss" lang="scss" scoped></style>
