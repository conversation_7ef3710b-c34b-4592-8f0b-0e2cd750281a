.download-app {
  width: 100%;
  padding-top: 145px;
  background-color: var(--vt-c-white-soft);
  position: relative;
  overflow: hidden;

  &__inner {
    background: url('../../images/BANNER_download_bg.png') center top no-repeat;
    background-size: cover;
  }
}

.content-wrapper {
  display: flex;
  justify-content: space-between;

  @media (max-width: 992px) {
    flex-direction: column;
    text-align: center;
  }
}

.text-content {
  flex: 1;
  padding-right: 40px;
  padding-top: 150px;
  padding-bottom: 159px;

  @media (max-width: 992px) {
    padding-right: 0;
  }
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  color: #0c3d18;
  line-height: 1.2;
  margin-bottom: 36px;
}

.section-description {
  font-size: 28px;
  line-height: 40px;
  color: #0c3d18;
  margin-bottom: 60px;
}

.app-buttons {
  display: flex;
  gap: 25px;

  @media (max-width: 992px) {
    justify-content: center;
  }

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: center;
  }
}

.app-button {
  display: block;
  transition: transform 0.3s ease;
  width: 280px;
  height: 90px;
  color: #ffffff;
  cursor: pointer;
  text-align: center;
  line-height: 90px;
  font-size: 22px;

  &:hover {
    transform: translateY(-5px);
  }
}

.phone-mockup {
  flex: 1;
  text-align: center;
  position: relative;

  &__inner {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
  }

  img {
    display: block;
    margin: auto;
  }
}
