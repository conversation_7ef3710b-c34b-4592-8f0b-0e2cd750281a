<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import EsimShare from '@/components/EsimShare/index.vue'

const { t } = useI18n()

const router = useRouter()
const handleClickRefer = function () {
  router.push({ name: 'refer-earn' })
}
</script>

<template>
  <section class="refer-a-friend">
    <div class="container-box">
      <div class="refer-inner">
        <div class="refer-img">
          <img src="../../images/BANNER_share_man.png" alt="" />
        </div>
        <div class="refer-content">
          <h3 class="refer-txt">
            {{ t('home.refer_friend.title') }}
            <span class="refer-txt__highlight">{{ t('home.refer_friend.highlight') }}</span>
          </h3>
          <EsimShare @click="handleClickRefer">{{ t('home.refer_friend.button') }}</EsimShare>
        </div>
      </div>
    </div>
  </section>
</template>

<style src="./ReferAFriend.scss" lang="scss" scoped></style>
