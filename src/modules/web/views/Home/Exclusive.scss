.hero-exclusive {
  width: 100%;
  background-image: url('../../images/BANNER_CEO.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

.exclusive-content {
  padding: 110px 0;
}

.exclusive-tag {
  font-size: 32px;
  font-weight: 700;
  color: #f6ff00;
  margin-bottom: 40px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.exclusive-title {
  display: flex;
  flex-direction: column;
  margin: 0 0 40px;

  .highlight {
    font-size: 64px;
    font-weight: 800;
    color: white;
    line-height: 1.2;
    margin-bottom: 8px;
  }

  .subtitle {
    font-size: 64px;
    font-weight: normal;
    color: white;
    line-height: 1.2;
  }

  .learn-more-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    color: #333;
    border: none;
    border-radius: 50px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      background-color: #f8f8f8;
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .arrow-icon {
      display: inline-flex;
      margin-left: 8px;
      color: var(--base-color);
    }
  }
}
