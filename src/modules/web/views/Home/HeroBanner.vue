<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import EsimShare from '@/components/EsimShare/index.vue'
import EsimSearch from '@/components/EsimSearch/index.vue'
import { Icon } from '@/components/Icon'

const { t } = useI18n()

const router = useRouter()
const handleClickRefer = function () {
  router.push({ name: 'refer-earn' })
}
</script>

<template>
  <section class="hero-banner">
    <div class="hero-container">
      <div class="hero-container-inner container-box">
        <!-- 左侧图片区域 -->
        <div class="hero-image">
          <img src="../../images/BANNER_top_man.png" />
        </div>

        <!-- 右侧内容区域 -->
        <div class="hero-content">
          <div class="hero-content-inner">
            <h1 class="hero-title">
              {{ t('home.title') }}
              <span class="hero-highlight fw-bold">{{ t('home.title_highlight') }}</span>
            </h1>

            <!-- 搜索框 -->
            <EsimSearch />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部促销条 -->
    <div class="container-box" style="position: relative">
      <div class="promo-container">
        <div class="promo-box">
          <span class="promo-text">{{ t('home.adventure') }}</span>
          <span class="promo-adventure">
            GCU50
            <Icon icon="svg-icon:copy"></Icon>
          </span>
        </div>

        <div class="promo-box">
          <span class="promo-text">{{ t('home.share_friend') }}</span>
          <EsimShare mode="plain" @click="handleClickRefer">{{
            t('home.refer_friend.button')
          }}</EsimShare>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss" scoped src="./HeroBanner.scss"></style>
