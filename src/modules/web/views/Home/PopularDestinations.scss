.popular-destinations {
  width: 100%;
  padding-top: 160px;
  background-color: var(--vt-c-white-soft);
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 50px;
  color: #000;
}

.tabs-container {
  display: flex;
  justify-content: center;
  margin-bottom: 25px;

  .custom-tabs {
    --el-color-primary: var(--base-color);
    // 自定义Element Plus Tabs样式
    :deep(.el-tabs__nav-wrap::after) {
      // 移除底部线条
      background-color: transparent;
    }

    :deep(.el-tabs__item) {
      font-size: 28px;
      font-weight: 500;
      color: #666666;
      padding: 0 90px 10px;
      width: 370px;

      &.is-active,
      &:hover {
        color: var(--el-color-primary);
      }
    }

    :deep(.el-tabs__active-bar) {
      height: 4px;
      border-radius: 100px;
      max-width: 40px;
      left: 74px;
    }
  }
}

.show-more-container {
  margin-top: 60px;
  display: flex;
  justify-content: center;
}
