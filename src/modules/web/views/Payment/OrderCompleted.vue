<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import { Icon } from '@/components/Icon/index'

const { t } = useI18n()
const router = useRouter()

// 模拟订单数据
const orderNumber = ref('202503291221070328855560')

// 导航到订单列表
const goToOrders = () => {
  router.push('/orders')
}

// 查看eSIM详情
const viewEsimDetails = () => {
  router.push({ name: 'esim-detail', params: { id: orderNumber.value } })
}
</script>

<template>
  <div class="order-completed">
    <div class="order-completed-content">
      <!-- 成功图标 -->
      <div class="success-icon">
        <div class="icon-circle">
          <Icon icon="svg-icon:complete" :size="72" color="#fff" />
        </div>
      </div>

      <!-- 成功标题 -->
      <h1 class="success-title">{{ t('order.completed_title') }}</h1>

      <div class="container-box">
        <div class="order-info-container">
          <!-- 左侧：订单信息 -->
          <div class="order-info-left">
            <h2 class="info-heading">{{ t('order.thank_you') }}</h2>
            <p class="info-text">{{ t('order.number') }} {{ orderNumber }}</p>

            <a class="orders-link" @click="goToOrders">
              {{ t('order.go_to_orders') }}
            </a>
          </div>

          <!-- 右侧：安装指南 -->
          <div class="order-info-right">
            <h2 class="info-heading">{{ t('order.how_to_install') }}</h2>
            <p class="info-text">
              {{ t('order.install_instruction') }}
            </p>

            <button class="esim-details-button" @click="viewEsimDetails">
              <span class="button-icon">
                <Icon icon="svg-icon:esim_stroke" :size="28" color="#fff" />
              </span>
              <span class="button-text">{{ t('order.esim_details') }}</span>
              <span class="button-arrow">
                <Icon icon="svg-icon:arrow_right" :size="22" color="#fff" />
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use 'sass:color';

.order-completed {
  margin-top: 80px;
}

.order-completed-content {
  width: 100%;
  line-height: 1;
  text-align: center;
}

.success-icon {
  margin-bottom: 48px;

  .icon-circle {
    width: 64px;
    height: 64px;
    background-color: #00c853;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
  }
}

.success-title {
  font-size: 36px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 80px;
}

.order-info-container {
  display: flex;
  justify-content: space-between;
  text-align: left;
  gap: 40px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 30px;
  }
}

.order-info-left,
.order-info-right {
  flex: 1;
  min-width: 0;
}

.info-heading {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 24px;
}

.info-text {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 48px;
}

.orders-link {
  font-size: 24px;
  line-height: 72px;
  color: var(--base-color);
  text-decoration: none;
  position: relative;
  cursor: pointer;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--base-color);
  }

  &:hover {
    color: color.adjust(#00c853, $lightness: 10%);

    &::after {
      background-color: color.adjust(#00c853, $lightness: 10%);
    }
  }
}

.esim-details-button {
  display: flex;
  align-items: center;
  border: none;
  border-radius: 20px;
  padding: 22px 28px;
  width: 400px;
  cursor: pointer;
  background-color: #222222;
  color: white;
  transition: all 0.3s ease;
  transition: all 0.3s ease;

  &:hover {
    background-color: #000;
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }

  .button-icon {
    margin-right: 28px;
  }

  .button-text {
    flex: 1;
    text-align: left;
    font-size: 24px;
    font-weight: 500;
  }

  .button-arrow {
    margin-left: auto;
  }
}
</style>
