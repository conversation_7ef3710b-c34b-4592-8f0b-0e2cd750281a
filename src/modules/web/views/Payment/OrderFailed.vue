<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const router = useRouter()

// 返回结账页面
const backToCheckout = () => {
  router.back()
}
</script>

<template>
  <div class="payment-failed">
    <div class="payment-failed-content">
      <!-- 错误图标 -->
      <div class="error-icon">
        <div class="icon-circle">
          <Icon icon="svg-icon:failed" :size="72" color="#fff" />
        </div>
      </div>

      <!-- 错误标题 -->
      <h1 class="error-title">{{ t('payment.failed_title') }}</h1>

      <!-- 错误信息 -->
      <p class="error-message">{{ t('payment.error_message') }}</p>
      <p class="error-description">
        {{ t('payment.error_description') }}
      </p>

      <!-- 返回按钮 -->
      <button class="back-button" @click="backToCheckout">
        {{ t('payment.back_to_checkout') }}
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.payment-failed {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 80px;
}

.payment-failed-content {
  text-align: center;
  line-height: 1;
}

.error-icon {
  margin-bottom: 48px;

  .icon-circle {
    width: 64px;
    height: 64px;
    background-color: #ff3d00;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
  }
}

.error-title {
  font-size: 36px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 48px;
}

.error-message {
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  margin-bottom: 16px;
}

.error-description {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.5);
}

.back-button {
  margin-top: 80px;
  background-color: #222222;
  color: white;
  border: none;
  border-radius: 36px;
  padding: 26px 42px;
  font-size: 24px;
  line-height: 1;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #000;
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}
</style>
