<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'

interface Props {
  visible: boolean
  currentName: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'save', name: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

// 表单数据
const formData = ref({
  fullName: '',
})

// 表单验证规则
const rules = {
  fullName: [
    { required: true, message: 'Please enter your full name', trigger: 'blur' },
    { min: 2, max: 50, message: 'Name should be 2-50 characters', trigger: 'blur' },
  ],
}

// 表单引用
const formRef = ref()

// 计算属性：控制弹窗显示
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 监听弹窗打开，初始化表单数据
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      formData.value.fullName = props.currentName
    }
  },
)

// 关闭弹窗
const closeModal = () => {
  emit('update:visible', false)
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 保存
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    emit('save', formData.value.fullName)
    closeModal()
  } catch (error) {
    console.log('Validation failed:', error)
  }
}

// 取消
const handleCancel = () => {
  closeModal()
}
</script>

<template>
  <el-dialog
    v-model="modalVisible"
    title="Edit Name"
    :show-close="false"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="edit-name-modal"
    @close="closeModal"
  >
    <div class="modal-content">
      <el-form ref="formRef" :model="formData" :rules="rules" class="edit-form">
        <el-form-item prop="fullName">
          <el-input
            v-model="formData.fullName"
            placeholder="Enter your full name"
            size="large"
            class="form-input"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button class="cancel-btn" @click="handleCancel"> Cancel </el-button>
        <el-button type="primary" class="save-btn" @click="handleSave"> Save </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped lang="scss">
.edit-form {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cancel-btn {
  padding: 0;
  border: none;
  background: transparent;
  color: var(--primary-color);
  font-size: 20px;
  font-weight: 500;

  &:hover {
    color: var(--primary-color);
    background: transparent;
  }
}

.save-btn {
  width: 280px;
  border-radius: 36px;
  background: var(--primary-color);
  border-color: var(--primary-color);
  font-size: 24px;
  font-weight: 600;
  color: white;
}
</style>
