<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter, useRoute } from 'vue-router'
import { ArrowRightBold } from '@element-plus/icons-vue'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()

// 用户信息数据
const userInfo = reactive({
  fullName: '<PERSON> Blu<PERSON>',
  username: 'GlocalMe001',
  email: '<EMAIL>',
  isVip: true,
  isUt: true,
})

// 侧边栏菜单项
const sidebarItems = ref([
  { key: 'orders', label: t('accountSettings.orders'), route: 'orders' },
  { key: 'myPromos', label: t('accountSettings.myPromos'), route: 'my-promos' },
  { key: 'pointsRewards', label: t('accountSettings.pointsRewards'), route: 'points-rewards' },
  { key: 'accountSettings', label: t('accountSettings.title'), route: 'account-settings' },
])

// 判断当前激活的菜单项
const isActive = (routeName: string) => {
  return route.name === routeName
}

// 处理侧边栏点击
const handleSidebarClick = (item: any) => {
  if (item.route) {
    router.push({ name: item.route })
  }
}
</script>

<template>
  <div class="sidebar">
    <!-- 用户信息卡片 -->
    <div class="user-card">
      <div class="avatar-section">
        <div class="avatar">
          <div class="avatar-placeholder">
            {{ userInfo.fullName.charAt(0).toUpperCase() }}
          </div>
        </div>
      </div>
      <div class="user-info">
        <h3 class="username">{{ userInfo.username }}</h3>
        <div class="badges">
          <span v-if="userInfo.isVip" class="badge vip">
            <Icon icon="svg-icon:vip"></Icon>
          </span>
          <span v-if="userInfo.isUt" class="badge ut">U1</span>
        </div>
      </div>
    </div>

    <!-- 菜单列表 -->
    <div class="menu-list">
      <div
        v-for="item in sidebarItems"
        :key="item.key"
        :class="['menu-item', { active: isActive(item.route) }]"
        @click="handleSidebarClick(item)"
      >
        <span class="menu-label">{{ item.label }}</span>
        <el-icon class="arrow-icon" :size="18" v-if="isActive(item.route)">
          <ArrowRightBold />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// 左侧边栏样式
.sidebar {
  width: 420px;
  flex-shrink: 0;
  background: white;
  border-radius: 20px;
  border: 1px solid #e0e0e0;
  padding: 60px 20px 24px 24px;
}

.user-card {
  margin-bottom: 60px;
}

.avatar-section {
  margin-bottom: 30px;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
  background: #f0f0f0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 32px;
  font-weight: 600;
}

.username {
  font-size: 24px;
  text-align: center;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 16px 0;
}

.badges {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.badge {
  width: 48px;
  height: 24px;
  line-height: 24px;
  border-radius: 12px;
  font-size: 18px;
  text-align: center;
  font-weight: 600;

  &.vip {
    background: #ffd700;
    color: #72440d;
    font-size: 24px;
  }

  &.ut {
    background: #00c65e;
    color: white;
  }
}

.menu-list {
  overflow: hidden;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 72px;
  line-height: 72px;
  padding: 0 30px 0 24px;
  cursor: pointer;
  border-radius: 20px;
  transition: all 0.3s ease;
  color: var(--primary-color);

  &:hover {
    background: rgba(0, 198, 94, 0.06);
    color: #00c65e;
  }

  &.active {
    background: rgba(0, 198, 94, 0.06);
    color: #00c65e;

    .arrow-icon {
      color: #00c851;
    }
  }
}

.menu-label {
  font-size: 20px;
  font-weight: 500;
}

.arrow-icon {
  font-size: 20px;
  font-weight: bold;
}

// 响应式设计
@media (max-width: 1024px) {
  .sidebar {
    width: 100%;
  }
}
</style>
