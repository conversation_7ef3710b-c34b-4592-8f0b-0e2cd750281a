<script setup lang="ts">
import { defineEmits, defineProps } from 'vue'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const tips = [
  {
    title: 'Stable Internet Required',
    description: 'Ensure your device has a stable connection to prevent eSIM installation errors.',
  },
  {
    title: 'Do Not Interrupt',
    description: 'Avoid disrupting the eSIM installation to prevent errors.',
  },
  {
    title: 'Do Not Delete',
    description:
      "Most eSIMs can be installed only once. Removing it means it can't be reinstalled.",
  },
  {
    title: 'Follow Instructions',
    description: 'Carefully follow all steps for successful eSIM activation.',
  },
]

const handleClose = () => {
  emit('update:visible', false)
}

const handleConfirm = () => {
  emit('confirm')
  emit('update:visible', false)
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    title=""
    width="600px"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="important-tips-modal"
    @update:model-value="handleClose"
  >
    <div class="modal-content">
      <h2 class="modal-title">Important Tips</h2>

      <div class="tips-list">
        <div v-for="(tip, index) in tips" :key="index" class="tip-item">
          <div class="tip-bullet">•</div>
          <div class="tip-content">
            <h3 class="tip-title">{{ tip.title }}</h3>
            <p class="tip-description">{{ tip.description }}</p>
          </div>
        </div>
      </div>

      <div class="modal-actions">
        <button class="confirm-btn" @click="handleConfirm">Got It</button>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss">
.important-tips-modal.el-dialog {
  border-radius: 20px;
  background: #f6f6f6;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 40px;
  }
}
</style>

<style scoped lang="scss">
.modal-content {
  text-align: center;
}

.modal-title {
  font-size: 28px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 32px 0;
}

.tips-list {
  text-align: left;
  margin-bottom: 40px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.tip-bullet {
  font-size: 20px;
  font-weight: bold;
  color: #000000;
  line-height: 1.2;
  margin-top: 2px;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.tip-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.modal-actions {
  text-align: center;
}

.confirm-btn {
  width: 100%;
  height: 56px;
  background: #000000;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #333333;
  }

  &:active {
    transform: translateY(1px);
  }
}
</style>
