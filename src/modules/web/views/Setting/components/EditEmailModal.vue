<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'

interface Props {
  visible: boolean
  currentEmail: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'save', email: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

// 表单数据
const formData = ref({
  verificationCode: '',
  newEmail: '',
})

// 发送验证码状态
const sendCodeState = ref({
  loading: false,
  countdown: 0,
  timer: null as NodeJS.Timeout | null,
})

// 表单验证规则
const rules = {
  verificationCode: [
    { required: true, message: 'Please enter verification code', trigger: 'blur' },
    { len: 6, message: 'Verification code must be 6 digits', trigger: 'blur' },
  ],
  newEmail: [
    { required: true, message: 'Please enter your new email address', trigger: 'blur' },
    { type: 'email', message: 'Please enter a valid email address', trigger: 'blur' },
  ],
}

// 表单引用
const formRef = ref()

// 计算属性：控制弹窗显示
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 计算属性：发送验证码按钮文本
const sendCodeButtonText = computed(() => {
  if (sendCodeState.value.loading) {
    return 'Sending...'
  }
  if (sendCodeState.value.countdown > 0) {
    return `Resend (${sendCodeState.value.countdown}s)`
  }
  return 'Send code'
})

// 计算属性：发送验证码按钮是否禁用
const sendCodeButtonDisabled = computed(() => {
  return sendCodeState.value.loading || sendCodeState.value.countdown > 0
})

// 监听弹窗打开，初始化表单数据
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      formData.value.verificationCode = ''
      formData.value.newEmail = ''
      resetSendCodeState()
    }
  },
)

// 重置发送验证码状态
const resetSendCodeState = () => {
  if (sendCodeState.value.timer) {
    clearInterval(sendCodeState.value.timer)
    sendCodeState.value.timer = null
  }
  sendCodeState.value.loading = false
  sendCodeState.value.countdown = 0
}

// 发送验证码
const handleSendCode = async () => {
  if (sendCodeButtonDisabled.value) return

  sendCodeState.value.loading = true

  try {
    // 这里应该调用API发送验证码
    await new Promise((resolve) => setTimeout(resolve, 1000)) // 模拟API调用

    // 开始倒计时
    sendCodeState.value.countdown = 60
    sendCodeState.value.timer = setInterval(() => {
      sendCodeState.value.countdown--
      if (sendCodeState.value.countdown <= 0) {
        resetSendCodeState()
      }
    }, 1000)

    console.log('Verification code sent to:', props.currentEmail)
  } catch (error) {
    console.error('Failed to send verification code:', error)
  } finally {
    sendCodeState.value.loading = false
  }
}

// 关闭弹窗
const closeModal = () => {
  emit('update:visible', false)
  resetForm()
  resetSendCodeState()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 保存
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 这里应该调用API验证验证码并更新邮箱
    console.log('Updating email with:', {
      verificationCode: formData.value.verificationCode,
      newEmail: formData.value.newEmail,
    })

    emit('save', formData.value.newEmail)
    closeModal()
  } catch (error) {
    console.log('Validation failed:', error)
  }
}

// 取消
const handleCancel = () => {
  closeModal()
}

// 组件卸载时清理定时器
onUnmounted(() => {
  resetSendCodeState()
})
</script>

<template>
  <el-dialog
    v-model="modalVisible"
    title="Edit Email"
    width="800px"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="edit-email-modal"
    @close="closeModal"
  >
    <div class="modal-content">
      <p class="description">
        Next, we'll send a verification code to your email. Please enter the 6-digit verification
        code we sent for verification.
      </p>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="top"
        class="edit-form"
      >
        <!-- 验证码输入 -->
        <div class="verification-row">
          <el-form-item prop="verificationCode" class="verification-input">
            <el-input
              v-model="formData.verificationCode"
              placeholder="Verification code"
              size="large"
              class="form-input"
              maxlength="6"
            />
          </el-form-item>
          <el-button
            :loading="sendCodeState.loading"
            :disabled="sendCodeButtonDisabled"
            class="send-code-btn"
            @click="handleSendCode"
          >
            {{ sendCodeButtonText }}
          </el-button>
        </div>

        <!-- 新邮箱输入 -->
        <el-form-item prop="newEmail">
          <el-input
            v-model="formData.newEmail"
            placeholder="New Email"
            size="large"
            class="form-input"
            type="email"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button class="cancel-btn" @click="handleCancel"> Cancel </el-button>
        <el-button type="primary" class="save-btn" @click="handleSave"> Save </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped lang="scss">
.edit-form {
  .verification-row {
    display: flex;
    gap: 16px;
    align-items: flex-start;
    margin-bottom: 40px;
  }

  .verification-input {
    flex: 1;
    margin-bottom: 0;
  }
}

.send-code-btn {
  width: 180px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.description {
  font-size: 20px;
  line-height: 28px;
  color: rgba(0, 0, 0, 0.5);
}
.cancel-btn {
  padding: 0;
  border: none;
  background: transparent;
  color: var(--primary-color);
  font-size: 20px;
  font-weight: 500;

  &:hover {
    color: var(--primary-color);
    background: transparent;
  }
}

.save-btn {
  width: 280px;
  border-radius: 36px;
  background: var(--primary-color);
  border-color: var(--primary-color);
  font-size: 24px;
  font-weight: 600;
  color: white;
}
</style>
