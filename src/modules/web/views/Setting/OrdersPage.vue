<script setup lang="ts">
import SettingSidebar from './components/SettingSidebar.vue'
import Pagination from '@/components/Pagination/index.vue'
import Copyright from '@web/components/Copyright.vue'
import Footer from '@web/components/Footer.vue'
import DownloadApp from '@web/views/Home/DownloadApp.vue'
import EsimEmpty from '@web/components/EsimEmpty.vue'
import { useI18n } from 'vue-i18n'
import { computed, onMounted, ref } from 'vue'
import type { OrderItem } from '@/api/Order/types.ts'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'

const { t } = useI18n()
const router = useRouter()

// 模拟订单数据
const orders = ref<OrderItem[]>([
  {
    orderId: '1',
    orderSN: 'ORD001',
    orderName: '5GB 3-Day for United States',
    orderStatus: 'COMPLETED',
    orderType: 'TOPUP',
    orderAmount: 900,
    currencyType: 'USD',
    createTime: Date.parse('2025/01/01'),
    payTime: Date.parse('2025/01/01'),
    completeTime: Date.parse('2025/01/01'),
  },
  {
    orderId: '2',
    orderSN: 'ORD002',
    orderName: '4GB 3-Day for Australia',
    orderStatus: 'COMPLETED',
    orderType: 'TOPUP',
    orderAmount: 1800,
    currencyType: 'USD',
    createTime: Date.parse('2025/01/01'),
    payTime: Date.parse('2025/01/01'),
    completeTime: Date.parse('2025/01/01'),
  },
  {
    orderId: '3',
    orderSN: 'ORD003',
    orderName: '20GB 3-Day for United Kingdom',
    orderStatus: 'COMPLETED',
    orderType: 'TOPUP',
    orderAmount: 18000,
    currencyType: 'USD',
    createTime: Date.parse('2025/01/01'),
    payTime: Date.parse('2025/01/01'),
    completeTime: Date.parse('2025/01/01'),
  },
  {
    orderId: '4',
    orderSN: 'ORD004',
    orderName: '4GB 3-Day for United States4GB 3-Day for United States',
    orderStatus: 'COMPLETED',
    orderType: 'TOPUP',
    orderAmount: 1588,
    currencyType: 'USD',
    createTime: Date.parse('2025/01/01'),
    payTime: Date.parse('2025/01/01'),
    completeTime: Date.parse('2025/01/01'),
  },
  {
    orderId: '5',
    orderSN: 'ORD005',
    orderName: '5GB 3-Day for United States',
    orderStatus: 'COMPLETED',
    orderType: 'TOPUP',
    orderAmount: 899,
    currencyType: 'USD',
    createTime: Date.parse('2025/01/01'),
    payTime: Date.parse('2025/01/01'),
    completeTime: Date.parse('2025/01/01'),
  },
])

// 分页相关
const pagination = ref({
  currentPage: 1,
  perPageCount: 5,
  totalCount: 12,
  totalPageCount: 3,
})

// 计算当前页显示的订单
const currentPageOrders = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.perPageCount
  const end = start + pagination.value.perPageCount
  return orders.value.slice(start, end)
})

// 格式化金额
const formatAmount = (amount: number, currency: string) => {
  const value = (amount / 100).toFixed(2)
  return `${currency} ${value}`
}

// 格式化日期
const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleDateString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

// 处理订单点击
const handleOrderClick = (order: OrderItem) => {
  console.log('Order clicked:', order)
  // 这里可以跳转到订单详情页面
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.value.currentPage = page
  // 这里应该调用API获取对应页的数据
  console.log('Page changed to:', page)
}

// 上一页
const handlePrevPage = () => {
  if (pagination.value.currentPage > 1) {
    handlePageChange(pagination.value.currentPage - 1)
  }
}

// 下一页
const handleNextPage = () => {
  if (pagination.value.currentPage < pagination.value.totalPageCount) {
    handlePageChange(pagination.value.currentPage + 1)
  }
}

// 跳转到第一页
const handleFirstPage = () => {
  handlePageChange(1)
}

// 跳转到最后一页
const handleLastPage = () => {
  handlePageChange(pagination.value.totalPageCount)
}

const handleShopPlans = () => {
  router.push('/shop-plans')
}

onMounted(() => {
  // 这里应该调用API获取订单数据
  console.log('Orders page mounted')
})
</script>

<template>
  <div class="account-settings-page">
    <div class="container-box">
      <div class="settings-layout">
        <!-- 左侧边栏 -->
        <SettingSidebar />

        <!-- 右侧内容区域 -->
        <div class="content-area">
          <div class="settings-content">
            <div class="orders-page">
              <div class="orders-header">
                <h2 class="orders-title">{{ t('orders.title') }}</h2>
              </div>
              <template v-if="currentPageOrders && currentPageOrders.length > 0">
                <div class="orders-list">
                  <div
                    v-for="order in currentPageOrders"
                    :key="order.orderId"
                    class="order-item"
                    @click="handleOrderClick(order)"
                  >
                    <div class="order-content">
                      <div class="order-info">
                        <h3 class="order-name">{{ order.orderName }}</h3>
                        <p class="order-date">{{ formatDate(order.createTime) }}</p>
                      </div>
                      <div class="order-amount">
                        {{ formatAmount(order.orderAmount, order.currencyType) }}
                      </div>
                      <div class="order-arrow">
                        <Icon icon="svg-icon:arrow_right" :size="30" />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 分页器 -->
                <Pagination
                  :current-page="pagination.currentPage"
                  :per-page-count="pagination.perPageCount"
                  :total-count="pagination.totalCount"
                  :total-page-count="pagination.totalPageCount"
                  translation-prefix="orders.pagination"
                  @page-change="handlePageChange"
                />
              </template>
              <div v-else class="order-empty">
                <EsimEmpty description="No orders found." />
                <el-button type="primary" class="btn btn-primary shop-btn" @click="handleShopPlans">
                  {{ t('myEsims.empty.shopPlans') }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <DownloadApp />
  <Footer />
  <Copyright />
</template>

<style scoped lang="scss">
.account-settings-page {
  padding: 72px 0 253px;
}

.settings-layout {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

// 右侧内容区域样式
.content-area {
  flex: 1;
  min-width: 0;
  width: 100%;
}

// 响应式设计
@media (max-width: 1024px) {
  .settings-layout {
    flex-direction: column;
    gap: 24px;
  }
}
</style>
<style scoped lang="scss">
.orders-page {
  padding: 0;
}

.orders-header {
  margin-bottom: 32px;
}

.orders-title {
  font-size: 32px;
  font-weight: 600;
  color: #000000;
  margin: 0;
}

.orders-list {
  margin-bottom: 24px;
}

.order-item {
  padding: 21px 24px;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  border: 1px solid #e0e0e0;
  margin-bottom: 24px;
}

.order-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.order-info {
  flex: 1;
  min-width: 0;
}

.order-name {
  font-size: 20px;
  font-weight: 500;
  color: var(--primary-color);
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.order-date {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.5);
  margin: 0;
}

.order-amount {
  font-size: 20px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.5);
  margin-right: 16px;
  white-space: nowrap;
}

.order-arrow {
  color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
}

.order-empty {
  text-align: center;

  .shop-btn {
    margin-top: 50px;
    width: 280px;
    font-size: 24px;
  }
}
</style>
