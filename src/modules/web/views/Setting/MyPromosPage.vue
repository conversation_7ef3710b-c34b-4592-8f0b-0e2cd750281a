<script setup lang="ts">
import SettingSidebar from './components/SettingSidebar.vue'
import Copyright from '@web/components/Copyright.vue'
import Footer from '@web/components/Footer.vue'
import DownloadApp from '@web/views/Home/DownloadApp.vue'
import { useI18n } from 'vue-i18n'
import { computed, onMounted, ref } from 'vue'
const { t } = useI18n()

// 总优惠券数量
const totalPromos = ref(12)

// 模拟优惠券数据
const promos = ref([
  {
    id: '1',
    type: 'percentage',
    value: '50%',
    label: 'off',
    code: 'GCU50',
    description: 'No-Minimum',
    validUntil: '04/02/2025',
    color: '#FF4757',
  },
  {
    id: '2',
    type: 'amount',
    value: 'USD 5',
    label: '',
    code: 'GCU50',
    description: 'No-Minimum',
    validUntil: '04/02/2025',
    color: '#FF4757',
  },
  {
    id: '3',
    type: 'amount',
    value: 'USD 10',
    label: '',
    code: 'GCU50',
    description: 'No-Minimum',
    validUntil: '04/02/2025',
    color: '#FF4757',
  },
  {
    id: '4',
    type: 'percentage',
    value: '20%',
    label: 'off',
    code: 'GCU50',
    description: 'No-Minimum',
    validUntil: '04/02/2025',
    color: '#FF4757',
  },
  {
    id: '5',
    type: 'amount',
    value: 'USD 10',
    label: '',
    code: 'GCU50',
    description: 'Minimum spending of USD 100',
    validUntil: '04/02/2025',
    color: '#FF4757',
  },
  {
    id: '6',
    type: 'amount',
    value: 'USD 50',
    label: '',
    code: 'GCU50',
    description: 'Minimum spending of USD 500',
    validUntil: '04/02/2025',
    color: '#FF4757',
  },
])

// 分页相关
const pagination = ref({
  currentPage: 1,
  perPageCount: 6,
  totalCount: 12,
  totalPageCount: 2,
})

// 兑换码输入
const redeemCode = ref('')
const isRedeeming = ref(false)

// 计算当前页显示的优惠券
const currentPagePromos = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.perPageCount
  const end = start + pagination.value.perPageCount
  return promos.value.slice(start, end)
})

// 分页处理
const handlePageChange = (page: number) => {
  pagination.value.currentPage = page
  console.log('Page changed to:', page)
}

// 上一页
const handlePrevPage = () => {
  if (pagination.value.currentPage > 1) {
    handlePageChange(pagination.value.currentPage - 1)
  }
}

// 下一页
const handleNextPage = () => {
  if (pagination.value.currentPage < pagination.value.totalPageCount) {
    handlePageChange(pagination.value.currentPage + 1)
  }
}

// 跳转到第一页
const handleFirstPage = () => {
  handlePageChange(1)
}

// 跳转到最后一页
const handleLastPage = () => {
  handlePageChange(pagination.value.totalPageCount)
}

// 兑换优惠券
const handleClaimPromo = async () => {
  if (!redeemCode.value.trim()) return

  isRedeeming.value = true
  try {
    console.log('Claim code:', redeemCode.value)
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))
    redeemCode.value = ''
  } catch (error) {
    console.error('Claim failed:', error)
  } finally {
    isRedeeming.value = false
  }
}

onMounted(() => {
  console.log('My Promos page mounted')
})
</script>

<template>
  <div class="account-settings-page">
    <div class="container-box">
      <div class="settings-layout">
        <!-- 左侧边栏 -->
        <SettingSidebar />

        <!-- 右侧内容区域 -->
        <div class="content-area">
          <div class="settings-content">
            <div class="my-promos-page">
              <div class="promos-header">
                <h2 class="promos-title">{{ t('myPromos.title') }} ({{ totalPromos }})</h2>
              </div>

              <!-- 优惠券网格 -->
              <div class="promos-grid">
                <div v-for="promo in currentPagePromos" :key="promo.id" class="promo-card">
                  <div class="promo-discount" :style="{ color: promo.color }">
                    <div class="discount-value">{{ promo.value }}</div>
                    <div v-if="promo.label" class="discount-label">{{ promo.label }}</div>
                  </div>

                  <div class="promo-info">
                    <div class="promo-code">{{ t('myPromos.promoCode') }}: {{ promo.code }}</div>
                    <div class="promo-condition">{{ promo.description }}</div>
                    <div class="promo-validity">
                      {{ t('myPromos.validUntil') }} {{ promo.validUntil }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 兑换区域 -->
              <div class="redeem-section">
                <div class="redeem-title">{{ t('myPromos.redeemTitle') }}</div>
                <div class="redeem-input-group">
                  <el-input
                    v-model="redeemCode"
                    :placeholder="t('myPromos.enterCode')"
                    class="redeem-input"
                    @keyup.enter="handleClaimPromo"
                  />
                  <el-button
                    type="primary"
                    class="claim-btn"
                    :loading="isRedeeming"
                    @click="handleClaimPromo"
                  >
                    {{ t('myPromos.claim') }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <DownloadApp />
  <Footer />
  <Copyright />
</template>

<style scoped lang="scss">
.account-settings-page {
  padding: 72px 0 253px;
}

.settings-layout {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

// 右侧内容区域样式
.content-area {
  flex: 1;
  min-width: 0;
  width: 100%;
}

// 响应式设计
@media (max-width: 1024px) {
  .settings-layout {
    flex-direction: column;
    gap: 24px;
  }
}
</style>
<style scoped lang="scss">
.my-promos-page {
  padding: 0;
}

.promos-header {
  margin-bottom: 32px;
}

.promos-title {
  font-size: 32px;
  font-weight: 600;
  color: #000000;
  margin: 0;
}

.promos-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 24px;
}

.promo-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.2s;

  &:hover {
    border-color: #00c851;
    box-shadow: 0 4px 12px rgba(0, 200, 81, 0.1);
  }
}

.promo-discount {
  text-align: center;
  min-width: 120px;
}

.discount-value {
  font-weight: 600;
  margin-bottom: 4px;
  font-size: 32px;
  color: #ff2424;
  line-height: 48px;
}

.discount-label {
  font-size: 24px;
  font-weight: 600;
  line-height: 24px;
}

.promo-info {
  flex: 1;
  border-left: dotted;
  border-color: #e0e0e0;
  padding-left: 24px;
}

.promo-type {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 27px;
}

.promo-code {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 21px;
}

.promo-condition {
  font-size: 18px;
  line-height: 18px;
  font-weight: 500;
  color: var(--primary-color);
  margin-bottom: 27px;
}

.promo-validity {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.5);
  line-height: 20px;
}

.double-arrow-left,
.arrow-left,
.arrow-right,
.double-arrow-right {
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
}

.redeem-section {
  margin-top: 100px;
}

.redeem-title {
  font-size: 24px;
  font-weight: 500;
  color: var(--primary-color);
  margin-bottom: 24px;
}

.redeem-input-group {
  display: flex;
  gap: 24px;
  align-items: center;
}

.redeem-input {
  flex: 1;
  max-width: 480px;

  :deep(.el-input__wrapper) {
    border-radius: 20px;
    border: 1px solid #00c65e;
    padding: 0 16px;

    &:hover {
      border-color: #00c851;
    }

    &.is-focus {
      border-color: #00c851;
      box-shadow: 0 0 0 2px rgba(0, 200, 81, 0.1);
    }
  }
}

.claim-btn {
  width: 160px;
  font-size: 24px;
  border-radius: 12px;

  &:hover {
    background: #333333;
  }

  &.is-loading {
    opacity: 0.7;
  }
}
</style>
