<script setup lang="ts">
import { reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import EditFullNameModal from './components/EditFullNameModal.vue'
import EditEmailModal from './components/EditEmailModal.vue'
import EditPasswordModal from './components/EditPasswordModal.vue'
import SettingSidebar from './components/SettingSidebar.vue'
import Copyright from '@web/components/Copyright.vue'
import Footer from '@web/components/Footer.vue'
import DownloadApp from '@web/views/Home/DownloadApp.vue'

const { t } = useI18n()
const router = useRouter()

// 用户信息数据
const userInfo = reactive({
  fullName: '<PERSON> Blunt',
  username: 'GlocalMe001',
  email: '<EMAIL>',
  password: '••••••••••••',
  placeOfResidence: 'United States',
  marketingEmails: true,
  avatar: '', // 头像占位符，使用CSS生成
  isVip: true,
  isUt: true,
})

// 弹窗状态
const modals = reactive({
  editFullName: false,
  editEmail: false,
  editPassword: false,
})

// 处理编辑操作
const handleEdit = (field: string) => {
  switch (field) {
    case 'fullName':
      modals.editFullName = true
      break
    case 'email':
      modals.editEmail = true
      break
    case 'password':
      modals.editPassword = true
      break
  }
}

// 处理营销邮件开关
const handleMarketingEmailsChange = (value: boolean) => {
  userInfo.marketingEmails = value
  console.log('Marketing emails:', value)
}

// 保存全名
const handleSaveFullName = (newName: string) => {
  userInfo.fullName = newName
  modals.editFullName = false
}

// 保存邮箱
const handleSaveEmail = (newEmail: string) => {
  userInfo.email = newEmail
  modals.editEmail = false
}

// 保存密码
const handleSavePassword = (passwordData: any) => {
  // 这里应该调用API更新密码
  console.log('Password update:', passwordData)
  modals.editPassword = false
}
</script>

<template>
  <div class="account-settings-page">
    <div class="container-box">
      <div class="settings-layout">
        <!-- 左侧边栏 -->
        <SettingSidebar />

        <!-- 右侧内容区域 -->
        <div class="content-area">
          <div class="settings-content">
            <!-- 账号设置页面 -->
            <div class="account-settings">
              <h1 class="page-title">{{ t('accountSettings.title') }}</h1>
              <!-- 设置项列表 -->
              <div class="settings-list">
                <!-- 全名 -->
                <div class="setting-item">
                  <div class="setting-info">
                    <label class="setting-label">{{ t('accountSettings.fullName') }}</label>
                    <span class="setting-value">{{ userInfo.fullName }}</span>
                  </div>
                  <el-button class="edit-btn" @click="handleEdit('fullName')">
                    {{ t('accountSettings.edit') }}
                  </el-button>
                </div>

                <!-- 邮箱地址 -->
                <div class="setting-item">
                  <div class="setting-info">
                    <label class="setting-label">{{ t('accountSettings.emailAddress') }}</label>
                    <span class="setting-value">{{ userInfo.email }}</span>
                  </div>
                  <el-button class="edit-btn" @click="handleEdit('email')">
                    {{ t('accountSettings.edit') }}
                  </el-button>
                </div>

                <!-- 密码 -->
                <div class="setting-item">
                  <div class="setting-info">
                    <label class="setting-label">{{ t('accountSettings.password') }}</label>
                    <span class="setting-value">{{ userInfo.password }}</span>
                  </div>
                  <el-button class="edit-btn" @click="handleEdit('password')">
                    {{ t('accountSettings.edit') }}
                  </el-button>
                </div>

                <!-- 居住地 -->
                <div class="setting-item">
                  <div class="setting-info">
                    <label class="setting-label">{{ t('accountSettings.placeOfResidence') }}</label>
                    <span class="setting-value">{{ userInfo.placeOfResidence }}</span>
                  </div>
                </div>

                <!-- 营销邮件开关 -->
                <div class="setting-item switch-item">
                  <div class="setting-info">
                    <label class="setting-label">{{ t('accountSettings.marketingEmails') }}</label>
                  </div>
                  <el-switch
                    v-model="userInfo.marketingEmails"
                    class="marketing-switch"
                    @change="handleMarketingEmailsChange"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <DownloadApp />
  <Footer />
  <Copyright />

  <!-- 编辑弹窗 -->
  <EditFullNameModal
    v-model:visible="modals.editFullName"
    :current-name="userInfo.fullName"
    @save="handleSaveFullName"
  />

  <EditEmailModal
    v-model:visible="modals.editEmail"
    :current-email="userInfo.email"
    @save="handleSaveEmail"
  />

  <EditPasswordModal v-model:visible="modals.editPassword" @save="handleSavePassword" />
</template>
<style lang="scss">
.edit-name-modal,
.edit-email-modal,
.edit-password-modal {
  border-radius: 20px;
  background: #f6f6f6;

  .el-dialog__header {
    text-align: left;
    padding: 60px 0 0;

    .el-dialog__title {
      font-size: 36px;
      font-weight: 600;
      color: var(--primary-color);
    }
  }

  .el-dialog__body {
    padding: 60px 0;
  }

  .el-dialog__footer {
    padding: 0 0 32px;
  }
}
</style>

<style scoped lang="scss">
.account-settings-page {
  padding: 72px 0 253px;
}

.settings-layout {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

// 右侧内容区域样式
.content-area {
  flex: 1;
  min-width: 0;
  width: 100%;
}

.page-title {
  font-size: 36px;
  line-height: 36px;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 36px 0;
  text-align: left;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100px;
  background: white;
  border-radius: 20px;
  padding: 24px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;

  &.switch-item {
    align-items: center;
  }

  &:last-child {
    .setting-label {
      margin-bottom: 0;
      font-size: 20px;
      color: var(--primary-color);
    }
  }
}

.setting-info {
  flex: 1;
}

.setting-label {
  display: block;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 16px;
}

.setting-value {
  font-size: 20px;
  color: var(--primary-color);
  font-weight: 500;
}

.edit-btn {
  height: 48px;
  line-height: 48px;
  width: 100px;
  text-align: center;
  border-radius: 20px;
  border: 1px solid var(--primary-color);
  background: white;
  color: var(--primary-color);
  font-size: 20px;

  &:hover {
    border-color: #00c851;
    color: #00c851;
  }
}

.marketing-switch {
  --el-switch-on-color: #00c851;

  :deep(.el-switch__core) {
    border-radius: 18px;
    width: 72px;
    height: 36px;

    .el-switch__action {
      width: 32px;
      height: 32px;
      left: calc(100% - 32px);
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .settings-layout {
    flex-direction: column;
    gap: 24px;
  }

  .sidebar {
    width: 100%;
  }

  .settings-content {
    padding: 24px;
  }

  .page-title {
    font-size: 24px;
  }
}
</style>
