<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Icon } from '@/components/Icon'
import DownloadApp from '@web/views/Home/DownloadApp.vue'
import Copyright from '@web/components/Copyright.vue'
import Footer from '@web/components/Footer.vue'
import EsimEmpty from '@web/components/EsimEmpty.vue'

const { t } = useI18n()
const router = useRouter()

// eSIM 数据 - 设置为空数组来测试空状态
const esimPlans = ref([
  // 有数据时的示例：
  {
    id: 1,
    title: '5GB 3-Day for United States',
    planType: 'Data Only',
    coverage: 'United States, Canada, Mexi...',
    expiry: '23 May 2025 | 02:54 (GMT)',
  },
  {
    id: 2,
    title: '5GB 3-Day for United States',
    planType: 'Data Only',
    coverage: 'United States, Canada, Mexi...',
    expiry: '23 May 2025 | 02:54 (GMT)',
  },
  {
    id: 3,
    title: '5GB 3-Day for United States',
    planType: 'Data Only',
    coverage: 'United States, Canada, Mexi...',
    expiry: '23 May 2025 | 02:54 (GMT)',
  },
])

// 计算属性：是否有数据
const hasPlans = computed(() => esimPlans.value.length > 0)

const handleDetails = (planId: number) => {
  router.push(`/esim-detail/${planId}`)
}

const handleBuyAgain = (planId: number) => {
  console.log('Buy again for plan:', planId)
  // TODO: 实现再次购买功能
}

const handleShopPlans = () => {
  router.push('/shop-plans')
}
</script>

<template>
  <div class="my-esims-page">
    <div class="container-box">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">{{ t('myEsims.title') }}</h1>
      </div>

      <!-- eSIM 卡片列表 -->
      <div v-if="hasPlans" class="esim-cards-container">
        <div v-for="plan in esimPlans" :key="plan.id" class="esim-card">
          <!-- 卡片图标 -->
          <Icon icon="svg-icon:eSIM" :size="72"></Icon>
          <!-- 卡片标题 -->
          <h3 class="card-title">{{ plan.title }}</h3>

          <!-- 分隔线 -->
          <div class="divider"></div>

          <!-- 卡片详情 -->
          <div class="card-details">
            <div class="detail-row">
              <span class="detail-label">{{ t('myEsims.planType') }}</span>
              <span class="detail-value">{{ plan.planType }}</span>
            </div>

            <div class="detail-row">
              <span class="detail-label">{{ t('myEsims.coverage') }}</span>
              <span class="detail-value">{{ plan.coverage }}</span>
            </div>

            <div class="detail-row">
              <span class="detail-label">{{ t('myEsims.expiry') }}</span>
              <span class="detail-value">{{ plan.expiry }}</span>
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="divider"></div>

          <!-- 操作按钮 -->
          <div class="card-actions">
            <el-Button type="primary" class="btn" @click="handleDetails(plan.id)">
              {{ t('myEsims.details') }}
            </el-Button>

            <el-Button class="btn" type="default" @click="handleBuyAgain(plan.id)">
              {{ t('myEsims.buyAgain') }}
            </el-Button>
          </div>
        </div>
      </div>
      <div v-else class="esim-empty">
        <EsimEmpty>
          <h2 class="esim-empty-title">{{ t('myEsims.empty.title') }}</h2>
          <p class="esim-empty-description">{{ t('myEsims.empty.description') }}</p>
          <el-button type="primary" class="btn btn-primary shop-btn" @click="handleShopPlans">
            {{ t('myEsims.empty.shopPlans') }}
          </el-button>
        </EsimEmpty>
      </div>
    </div>
  </div>
  <DownloadApp />
  <Footer />
  <Copyright />
</template>

<style scoped lang="scss">
.my-esims-page {
  padding: 48px 0 130px;
}

.page-header {
  text-align: center;
  margin-bottom: 48px;
}

.page-title {
  font-size: 36px;
  line-height: 36px;
  font-weight: bold;
  color: var(--primary-color);
  margin: 0;
}

.esim-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
  margin-bottom: 100px;
  margin-left: auto;
  margin-right: auto;
}

.esim-card {
  background: white;
  border-radius: 24px;
  padding: 40px 24px;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  border: 1px solid #e0e0e0;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
  }
}

.card-title {
  font-size: 22px;
  font-weight: 600;
  color: #212529;
  text-align: left;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin: 35px 0 24px 0;
  line-height: 1.3;
}

.divider {
  height: 1px;
  background: #e0e0e0;
  margin: 25px 0 30px 0;
}

.card-details {
  margin-bottom: 24px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 20px;
  color: #000000;
  font-weight: 500;
  flex-shrink: 0;
}

.detail-value {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-weight: 400;
  text-align: right;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 60%;
  line-height: 1.4;
}

.card-actions {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 32px;

  .btn-secondary {
    background: #ffffff;
  }
}

.esim-empty {
  text-align: center;
  margin: 70px auto 0;

  &-title {
    font-size: 36px;
    font-weight: 600;
    color: #000000;
    margin: 0 0 48px 0;
  }

  &-description {
    font-size: 20px;
    line-height: 1;
    color: rgba(0, 0, 0, 0.5);
    margin: 0 0 80px 0;
  }

  .shop-btn {
    width: 280px;
    font-size: 24px;
  }
}
</style>
