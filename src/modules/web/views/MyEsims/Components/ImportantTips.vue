<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

// 计算属性：控制弹窗显示
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 关闭弹窗
const closeModal = () => {
  emit('update:visible', false)
}

// 确认按钮
const handleConfirm = () => {
  emit('confirm')
  closeModal()
}

// 重要提示列表
const importantTips = computed(() => [
  {
    title: t('importantTips.stableInternet.title'),
    description: t('importantTips.stableInternet.description'),
  },
  {
    title: t('importantTips.doNotInterrupt.title'),
    description: t('importantTips.doNotInterrupt.description'),
  },
  {
    title: t('importantTips.doNotDelete.title'),
    description: t('importantTips.doNotDelete.description'),
  },
  {
    title: t('importantTips.followInstructions.title'),
    description: t('importantTips.followInstructions.description'),
  },
])
</script>

<template>
  <el-dialog
    v-model="modalVisible"
    :title="t('importantTips.title')"
    width="680px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    class="important-tips-dialog"
    @close="closeModal"
  >
    <div class="tips-content">
      <div v-for="(tip, index) in importantTips" :key="index" class="tip-item">
        <div class="tip-bullet">•</div>
        <div class="tip-text">
          <h3 class="tip-title">{{ tip.title }}</h3>
          <p class="tip-description">{{ tip.description }}</p>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" class="confirm-btn" @click="handleConfirm">
          {{ t('importantTips.gotIt') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss">
.important-tips-dialog {
  .el-dialog__header {
    padding: 28px 32px 0;
    text-align: center;

    .el-dialog__title {
      font-size: 36px;
      font-weight: 600;
      color: #000000;
      line-height: 1.2;
    }
  }

  .el-dialog__body {
    padding: 38px 0;
  }

  .el-dialog__footer {
    padding-bottom: 32px;
    text-align: center;
    padding-top: 10px;
  }
}
</style>

<style scoped lang="scss">
.tips-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.tip-bullet {
  font-size: 24px;
  font-weight: bold;
  color: #000000;
  line-height: 1.2;
  margin-top: 0;
  flex-shrink: 0;
}

.tip-text {
  flex: 1;
}

.tip-title {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  line-height: 1.3;
}

.tip-description {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.5);
  margin: 0;
  line-height: 28px;
}

.dialog-footer {
  width: 100%;
}

.confirm-btn {
  width: 100%;
}

// 响应式设计
@media (max-width: 768px) {
  .important-tips-dialog {
    .el-dialog__header {
      padding: 24px 24px 0;

      .el-dialog__title {
        font-size: 24px;
      }
    }

    .el-dialog__body {
      padding: 0;
    }

    .el-dialog__footer {
      padding-bottom: 30px;
    }
  }

  .tips-content {
    gap: 32px;
  }

  .tip-item {
    gap: 12px;
  }

  .tip-bullet {
    font-size: 20px;
  }

  .tip-title {
    font-size: 18px;
  }

  .tip-description {
    font-size: 14px;
  }
}
</style>
