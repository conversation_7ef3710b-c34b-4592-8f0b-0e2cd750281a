<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'

interface Props {
  visible: boolean
  deviceType: 'ios' | 'android'
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

// 当前选择的安装方式
const installMethod = ref<'qr' | 'manual'>('qr')

// eSIM 安装信息
const esimInfo = ref({
  smDpAddress: 'sm-prod.ondemandconnectivity.com',
  activationCode: 'FB890C197B97F1CB23F916699D0AEA186BD65 72D638C040594725AF805885E0',
})

// 设备版本选项
const deviceVersions = ref([
  { key: 'ios17', label: 'iOS 17 and later', active: true },
  { key: 'ios16', label: 'iOS 16', active: false },
  { key: 'ios15', label: 'iOS 15 and previous', active: false },
])
// android设备版本
const androidDeviceVersions = ref([
  { key: 'Google Pixel', label: 'Google Pixel', active: true },
  { key: 'Samsung', label: 'Samsung', active: false },
])

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})
watch(
  () => props.deviceType,
  (value) => {
    if (value === 'ios') {
      androidDeviceVersions.value.forEach((version) => {
        version.active = false
      })
      deviceVersions.value[0].active = true
    } else {
      deviceVersions.value.forEach((version) => {
        version.active = false
      })
      androidDeviceVersions.value[0].active = true
    }
  },
)
const deviceTitle = computed(() => {
  return props.deviceType === 'ios' ? 'iOS Device' : 'Android Device'
})

// 方法
const closeModal = () => {
  emit('close')
}

const switchMethod = (method: 'qr' | 'manual') => {
  installMethod.value = method
}

const selectVersion = (versionKey: string) => {
  ;(props.deviceType == 'ios' ? deviceVersions.value : androidDeviceVersions.value).forEach(
    (version) => {
      version.active = version.key === versionKey
    },
  )
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    // TODO: 显示复制成功提示
    console.log('Copied to clipboard:', text)
  } catch (err) {
    console.error('Failed to copy:', err)
  }
}

const goToStepGuide = () => {
  // TODO: 跳转到详细步骤指南
  console.log('Go to step-by-step guide')
}
const saveImage = () => {
  // TODO: 保存图片
  console.log('Save image')
}
</script>

<template>
  <el-dialog
    v-model="modalVisible"
    :title="deviceTitle"
    width="800px"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    class="installation-dialog"
    @close="closeModal"
  >
    <!-- 安装方式切换 -->
    <div class="method-switch">
      <button
        :class="['method-btn', { active: installMethod === 'qr' }]"
        @click="switchMethod('qr')"
      >
        {{ t('installModal.qrCode') }}
      </button>
      <button
        :class="['method-btn', { active: installMethod === 'manual' }]"
        @click="switchMethod('manual')"
      >
        {{ t('installModal.manual') }}
      </button>
    </div>
    <div class="inner-content">
      <!-- 警告信息 -->
      <div class="warning-section">
        <p class="warning-text">
          <span class="warning-label">{{ t('installModal.warning') }}</span>
          {{ t('installModal.warningText') }}
        </p>
      </div>

      <!-- iOS 版本选择 -->
      <div v-if="deviceType === 'ios'" class="version-section">
        <div class="version-tabs">
          <el-button
            v-for="version in deviceVersions"
            :key="version.key"
            type="text"
            :class="['version-tab', { active: version.active }]"
            @click="selectVersion(version.key)"
          >
            {{ version.label }}
          </el-button>
        </div>
      </div>
      <!-- iOS 版本选择 -->
      <div v-if="deviceType === 'android'" class="version-section">
        <div class="version-tabs">
          <el-button
            v-for="version in androidDeviceVersions"
            :key="version.key"
            type="text"
            :class="['version-tab', { active: version.active }]"
            @click="selectVersion(version.key)"
          >
            {{ version.label }}
          </el-button>
        </div>
      </div>
      <!-- QR Code 内容 -->
      <div v-if="installMethod === 'qr'" class="qr-section">
        <div class="qr-placeholder">
          <!-- 这里应该是实际的 QR Code -->
          <div class="qr-code">
            <svg width="200" height="200" viewBox="0 0 200 200" fill="none">
              <rect width="200" height="200" fill="white" />
              <rect x="20" y="20" width="20" height="20" fill="black" />
              <rect x="60" y="20" width="20" height="20" fill="black" />
              <rect x="100" y="20" width="20" height="20" fill="black" />
              <rect x="140" y="20" width="20" height="20" fill="black" />
              <rect x="180" y="20" width="20" height="20" fill="black" />
              <!-- 简化的 QR Code 样式 -->
            </svg>
          </div>
        </div>
        <p class="qr-tip">{{ t('installModal.qrTip') }}</p>

        <div>
          <el-button class="guide-btn" type="primary" @click="saveImage">
            {{ t('installModal.saveImage') }}
          </el-button>
        </div>
      </div>

      <!-- Manual 内容 -->
      <div v-if="installMethod === 'manual'" class="manual-section">
        <!-- SM-DP+ Address -->
        <div class="info-item">
          <label class="info-label">{{ t('installModal.smDpAddress') }}</label>
          <div class="info-value">
            <span class="info-text">{{ esimInfo.smDpAddress }}</span>
            <el-button
              type="primary"
              class="copy-btn"
              @click="copyToClipboard(esimInfo.smDpAddress)"
            >
              {{ t('installModal.copy') }}
            </el-button>
          </div>
        </div>

        <!-- Activation Code -->
        <div class="info-item">
          <label class="info-label">{{ t('installModal.activationCode') }}</label>
          <div class="info-value">
            <span class="info-text">{{ esimInfo.activationCode }}</span>
            <el-button
              type="primary"
              class="copy-btn"
              @click="copyToClipboard(esimInfo.activationCode)"
            >
              {{ t('installModal.copy') }}
            </el-button>
          </div>
        </div>

        <!-- 安装说明 -->
        <div class="install-note">
          <p>{{ t('installModal.installNote') }}</p>
        </div>
      </div>

      <!-- 安装步骤 -->
      <div class="steps-section">
        <h3 class="steps-title">{{ t('installModal.steps.installTitle') }}</h3>
        <div class="steps-content">
          <ol class="steps-list">
            <li>{{ t('installModal.steps.step1') }}</li>
            <li>{{ t('installModal.steps.step2') }}</li>
            <li>{{ t('installModal.steps.step3') }}</li>
            <li>{{ t('installModal.steps.step4') }}</li>
            <li>{{ t('installModal.steps.step5') }}</li>
            <li>{{ t('installModal.steps.step6') }}</li>
          </ol>
        </div>

        <h3 class="steps-title">{{ t('installModal.steps.accessTitle') }}</h3>
        <div class="steps-content">
          <ol class="steps-list">
            <li>{{ t('installModal.steps.access1') }}</li>
            <li>{{ t('installModal.steps.access2') }}</li>
            <li>{{ t('installModal.steps.access3') }}</li>
          </ol>
        </div>
      </div>
      <div>
        <el-button class="step-btn" type="primary" @click="goToStepGuide">
          {{ t('installModal.stepGuide') }}
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss">
// Element Plus Dialog 自定义样式
.installation-dialog {
  .el-dialog__header {
    padding: 24px 24px 24px;
    text-align: center;
  }
  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 16px 24px 24px;
    border-top: 1px solid #e5e7eb;
  }
}
.inner-content {
  overflow: auto;
  max-height: 60vh;
}
.method-switch {
  display: flex;
  background: #f3f4f6;
  border-radius: 20px;
  padding: 4px;
  margin: 0 0 36px;

  border: 1px solid #e0e0e0;
}

.method-btn {
  flex: 1;
  height: 72px;
  line-height: 72px;
  border: none;
  background: none;
  border-radius: 16px;
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    background: #111827;
    color: white;
  }
}
.warning-text {
  margin: 0;
  font-size: 20px;
  color: #ff2424;
  line-height: 28px;
}
.version-section {
  margin: 36px auto 24px auto;
}
.version-tab {
  padding: 8px 16px;
  width: 40%;
  font-size: 20px;
  color: var(--el-text-color-primary);
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    position: relative;
    color: var(--base-color);
    &:after {
      height: 4px;
      background: var(--base-color);
      border-radius: 2px;
      content: '';
      position: absolute;
      bottom: 10px;
      left: 50%;
      right: 0;
      width: 30%;
      transform: translate(-50%, -50%);
    }
  }
}

.qr-section {
  padding: 0 24px 48px;
  text-align: center;
}
.qr-tip {
  font-size: 20px;
  color: rgb(0, 0, 0, 0.5);
  line-height: 28px;
  text-align: left;
  margin-bottom: 36px;
}
.qr-placeholder {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.qr-code {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

.manual-section {
  padding: 0 24px 24px;
}

.info-item {
  margin-bottom: 20px;
  background: #f9fafb;
  padding: 24px 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.info-label {
  display: block;
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.info-value {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-text {
  flex: 1;
  font-size: 24px;
  font-weight: bold;
  color: var(--el-button-hover-bg-color);
  word-break: break-all;
}

.copy-btn {
  height: 48px;
  line-height: 48px;
  font-size: 20px;
  font-weight: 500;
  width: 112px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.install-note {
  margin-top: 24px;
  p {
    color: rgba(0, 0, 0, 0.5);
    font-size: 20px;
    line-height: 28px;
  }
}

.steps-section {
  padding: 0 24px 10px;
}

.steps-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  background: #f6f6f6;
  height: 75px;
  padding: 0 16px;
  border-radius: 20px;
  line-height: 75px;
  margin-bottom: 24px;
}

.steps-content {
  margin-bottom: 24px;
}

.steps-list {
  margin-bottom: 40px;
  padding-left: 0;

  li {
    font-size: 20px;
    color: rgba(0, 0, 0, 0.5);
    line-height: 28px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
.version-tabs {
  margin-bottom: 34px;
  display: flex;
  justify-content: space-around;
}
.guide-btn {
  width: 360px;
}
.step-btn {
  width: 100%;
  margin-bottom: 60px;
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.installation-dialog) {
    .el-dialog__header {
      padding: 16px 16px 12px;
    }

    .el-dialog__title {
      font-size: 20px;
    }

    .el-dialog__footer {
      padding: 12px 16px 16px;
    }
  }

  .method-switch,
  .warning-section,
  .version-section,
  .qr-section,
  .manual-section,
  .steps-section {
    margin-left: 16px;
    margin-right: 16px;
  }
  .version-tab {
    text-align: center;
  }
}
</style>
