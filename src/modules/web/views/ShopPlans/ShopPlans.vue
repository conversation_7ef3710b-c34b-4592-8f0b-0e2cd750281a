<script lang="ts" setup>
import EsimSlider from '@/components/EsimSlider/index.vue'
import EsimStages from '@/components/EsimStages/index.vue'
import EsimDestination from '../../components/EsimDestination.vue'
import { useI18n } from 'vue-i18n'
import { useDestinations, useDestinationNavigation } from '@/hooks/useDestinations'

const { t } = useI18n()

// 使用目的地数据 hooks
const {
  loading,
  error,
  filteredDestinations,
  setActiveTab
} = useDestinations()

// 使用目的地导航 hooks
const { goToPlanDetail } = useDestinationNavigation()

// 处理目的地点击
const handleTo = (item: any) => {
  goToPlanDetail(item)
}

// 处理标签切换
const handleTabChange = (tabIndex: number) => {
  // 根据索引设置对应的标签类型
  const tabType = tabIndex === 0 ? 'local' : 'region'
  setActiveTab(tabType as any)
}

// 处理过滤器变化
const handleFilter = function (item) {
  // 这里可以添加其他过滤逻辑
  console.log(item.value)
}
</script>

<template>
  <div class="shop-plans">
    <EsimSlider style="width: 646px" :list="[t('home.popular.local'), t('home.popular.regional')]"
      @change="handleTabChange">
    </EsimSlider>

    <div style="margin: 56px 0">
      <EsimStages @change="handleFilter"></EsimStages>
    </div>

    <div class="container-box">
      <!-- 错误提示 -->
      <div v-if="error" class="error-message">
        <p>{{ t('common.error_loading_data') || '加载数据时出错，请稍后重试' }}</p>
      </div>

      <!-- 目的地列表 -->
      <EsimDestination v-else v-loading="loading" :list="filteredDestinations" @click-cell="handleTo">
      </EsimDestination>

      <!-- 无数据提示 -->
      <div v-if="!loading && !error && filteredDestinations.length === 0" class="no-data">
        <p>{{ t('common.no_data') || '暂无数据' }}</p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.shop-plans {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 160px;
}

.error-message {
  text-align: center;
  padding: 40px 20px;
  color: #f56565;
  background-color: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  margin: 20px 0;

  p {
    margin: 0;
    font-size: 16px;
  }
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #718096;

  p {
    margin: 0;
    font-size: 16px;
  }
}
</style>
