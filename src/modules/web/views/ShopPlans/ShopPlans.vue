<script lang="ts" setup>
import EsimSlider from '@/components/EsimSlider/index.vue'
import EsimStages from '@/components/EsimStages/index.vue'
import EsimDestination from '../../components/EsimDestination.vue'
import { destinations } from '@/data/destinations'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
const { t } = useI18n()

const router = useRouter()
const handleTo = (item) => {
  router.push({
    name: 'plan-detail',
    params: {
      pid: item.id,
    },
    query: {
      name: item.name,
    },
  })
}

const handleFilter = function () {}
</script>

<template>
  <div class="shop-plans">
    <EsimSlider style="width: 646px" :list="[t('home.popular.local'), t('home.popular.regional')]">
    </EsimSlider>

    <div style="margin: 56px 0">
      <EsimStages @change="handleFilter"></EsimStages>
    </div>

    <div class="container-box">
      <EsimDestination :list="destinations" @click-cell="handleTo"></EsimDestination>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.shop-plans {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 160px;
}
</style>
