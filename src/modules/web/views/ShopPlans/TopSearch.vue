<script lang="ts" setup>
import EsimSearch from '@/components/EsimSearch/index.vue'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()
</script>

<template>
  <div class="top-search">
    <div class="container-box">
      <h1 class="title">{{ t('home.title') }} {{ t('home.title_highlight') }}</h1>
      <EsimSearch align="center" style="height: 80px" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.top-search {
  text-align: center;
  padding-top: 100px;
  padding-bottom: 100px;

  .title {
    font-size: 64px;
    color: #000000;
    line-height: 1;
    font-weight: bold;
    margin-bottom: 48px;
  }
}
</style>
