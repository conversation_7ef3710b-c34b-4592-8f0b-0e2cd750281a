<script lang="ts" setup>
import { Icon } from '@/components/Icon/index'
import FixedBottom from '../../components/FixedBottom.vue'
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

interface Plan {
  id: number
  data: string
  days: string
  price: number
  originalPrice?: number
  onsale?: boolean
  isSelected?: boolean
}

interface RegionalPlan extends Plan {
  coverage: {
    region: string
    countries: number
  }
}

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

const localPlans = ref<Plan[]>([
  {
    id: 1,
    data: '5GB',
    days: '3 Days',
    price: 11.0,
    originalPrice: 22.0,
    onsale: true,
    isSelected: false,
  },
  { id: 2, data: '10GB', days: '7 Days', price: 29.0, isSelected: false },
  { id: 3, data: '20GB', days: '7 Days', price: 49.0, isSelected: false },
  { id: 4, data: '50GB', days: '30 Days', price: 89.0, isSelected: false },
])

const regionalPlans = ref<RegionalPlan[]>([
  {
    id: 5,
    data: '10GB',
    days: '30 Days',
    price: 29.0,
    isSelected: false,
    coverage: {
      region: 'North America',
      countries: 3,
    },
  },
  {
    id: 6,
    data: '20GB',
    days: '30 Days',
    price: 69.0,
    isSelected: false,
    coverage: {
      region: 'Europe',
      countries: 40,
    },
  },
])

const allPlans = localPlans.value.concat(regionalPlans.value)

const selectPlan = (plan: Plan | RegionalPlan) => {
  allPlans.forEach((p) => (p.isSelected = p.id === plan.id))
}

const currentPlan = computed(() => {
  return allPlans.find((item) => item.isSelected)
})

const countryName = computed(() => {
  return route.query.name || '--'
})

const handleNextTo = function () {
  if (!currentPlan.value?.id) return
  router.push({ name: 'checkout', params: { id: currentPlan.value.id }, query: route.query })
}
const handleOpenRegion = function (plan) {
  console.log('ddd ==> plan', plan)
}
</script>

<template>
  <div class="shop-detail">
    <div class="container-box">
      <h1 class="shop-title">{{ countryName }}</h1>

      <!-- Local Plans -->
      <div class="plans-section">
        <h2 class="section-title">Local Plans</h2>
        <div class="cards-container">
          <div
            v-for="plan in localPlans"
            :key="plan.id"
            class="plan-card"
            :class="{ selected: plan.isSelected }"
            @click="selectPlan(plan)"
          >
            <div class="plan-data">{{ plan.data }}</div>
            <div class="plan-inner">
              <div v-if="plan.onsale" class="hot-onsale">
                <span class="hot-network">5G</span>
                <span class="hot-text">ON SALE</span>
              </div>
              <div class="plan-days">{{ plan.days }}</div>
            </div>

            <div class="plan-price">
              <span class="currency">USD</span>
              <span class="price">{{ plan.price.toFixed(2) }}</span>
              <span v-if="plan.originalPrice" class="original-price">{{
                plan.originalPrice.toFixed(2)
              }}</span>
            </div>

            <div class="plan-indicator">
              <div class="circle" :class="{ checked: plan.isSelected }">
                <Icon v-if="plan.isSelected" icon="svg-icon:complete" size="32" color="#fff"></Icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Regional Plans -->
      <div class="plans-section">
        <h2 class="section-title">Regional Plans</h2>
        <div class="cards-container">
          <div
            v-for="plan in regionalPlans"
            :key="plan.id"
            class="plan-card"
            :class="{ selected: plan.isSelected }"
            @click="selectPlan(plan)"
          >
            <div class="plan-data">{{ plan.data }}</div>
            <div class="plan-days">{{ plan.days }}</div>

            <div class="coverage-info">
              <div class="coverage-label">Coverage:</div>
              <div class="coverage-region">
                <span class="region-link" @click.stop="handleOpenRegion(plan)">
                  {{ plan.coverage.region }} ({{ plan.coverage.countries }})
                  <span class="arrow-icon">›</span>
                </span>
              </div>
            </div>

            <div class="plan-price">
              <span class="currency">USD</span>
              <span class="price">{{ plan.price.toFixed(2) }}</span>
            </div>

            <div class="plan-indicator">
              <div class="circle" :class="{ checked: plan.isSelected }">
                <Icon v-if="plan.isSelected" icon="svg-icon:complete" size="32" color="#fff"></Icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <FixedBottom :disabled="!currentPlan" :text="t('checkout.title')" @click-button="handleNextTo">
      <div v-if="currentPlan" class="selected">
        <div class="selected-plan">
          <span class="selected-plan-country">{{ countryName }}</span>
          <div class="selected-plan-info">{{ currentPlan.data }} / {{ currentPlan.days }}</div>
        </div>

        <div class="selected-total">
          <span class="selected-total-label">{{ t('checkout.total') }}</span>
          <div class="selected-total-value">
            <span class="currency">USD</span>{{ currentPlan.price.toFixed(2) }}
          </div>
        </div>
      </div>
    </FixedBottom>
  </div>
</template>

<style src="../../styles/shop-common.scss" scoped lang="scss"></style>
<style src="./PlanDetail.scss" scoped lang="scss"></style>
