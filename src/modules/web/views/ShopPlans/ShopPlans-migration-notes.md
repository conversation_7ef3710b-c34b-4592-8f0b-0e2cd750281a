# ShopPlans.vue 迁移说明

## 迁移概述

将 `ShopPlans.vue` 从使用静态数据迁移到使用 `useDestinations` hooks，实现动态数据获取和更好的用户体验。

## 主要变更

### 1. 数据源变更

**迁移前:**
```javascript
import { destinations } from '@/data/destinations'
// 使用静态数据
<EsimDestination :list="destinations" @click-cell="handleTo"></EsimDestination>
```

**迁移后:**
```javascript
import { useDestinations, useDestinationNavigation } from '@/hooks/useDestinations'

const {
  loading,
  error,
  filteredDestinations,
  setActiveTab
} = useDestinations()

// 使用动态数据
<EsimDestination v-loading="loading" :list="filteredDestinations" @click-cell="handleTo">
```

### 2. 导航功能改进

**迁移前:**
```javascript
const router = useRouter()
const handleTo = (item) => {
  router.push({
    name: 'plan-detail',
    params: { pid: item.id },
    query: { name: item.name }
  })
}
```

**迁移后:**
```javascript
const { goToPlanDetail } = useDestinationNavigation()

const handleTo = (item: any) => {
  goToPlanDetail(item)
}
```

### 3. 标签切换功能

**新增功能:**
```javascript
const handleTabChange = (tabIndex: number) => {
  const tabType = tabIndex === 0 ? 'local' : 'region'
  setActiveTab(tabType as any)
}
```

**模板更新:**
```html
<EsimSlider 
  style="width: 646px" 
  :list="[t('home.popular.local'), t('home.popular.regional')]"
  @change="handleTabChange">
</EsimSlider>
```

### 4. 错误处理和用户体验

**新增功能:**
- 加载状态显示
- 错误信息提示
- 无数据状态提示

```html
<!-- 错误提示 -->
<div v-if="error" class="error-message">
  <p>{{ t('common.error_loading_data') || '加载数据时出错，请稍后重试' }}</p>
</div>

<!-- 目的地列表 -->
<EsimDestination 
  v-else
  v-loading="loading" 
  :list="filteredDestinations" 
  @click-cell="handleTo">
</EsimDestination>

<!-- 无数据提示 -->
<div v-if="!loading && !error && filteredDestinations.length === 0" class="no-data">
  <p>{{ t('common.no_data') || '暂无数据' }}</p>
</div>
```

## 新增样式

```scss
.error-message {
  text-align: center;
  padding: 40px 20px;
  color: #f56565;
  background-color: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  margin: 20px 0;
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}
```

## 功能改进

### 1. 动态数据获取
- 从静态数据改为 API 动态获取
- 支持数据缓存（50秒）
- 自动错误处理

### 2. 交互体验提升
- 标签切换功能正常工作
- 加载状态可视化
- 错误状态友好提示
- 空数据状态提示

### 3. 代码复用性
- 使用可复用的 hooks
- 统一的导航逻辑
- 一致的数据处理方式

### 4. 类型安全
- TypeScript 类型支持
- 更好的开发体验

## 兼容性

- 保持原有的 UI 布局和样式
- 保持原有的组件接口
- 向后兼容的功能增强

## 测试建议

1. 测试标签切换功能
2. 测试加载状态显示
3. 测试错误状态处理
4. 测试目的地点击导航
5. 测试无数据状态显示
