<template>
  <el-config-provider :locale="elementLocale">
    <!--    <el-select v-model="locale" placeholder="&#45;&#45;">-->
    <!--      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />-->
    <!--    </el-select>-->

    <RouterView />
  </el-config-provider>
</template>

<script setup lang="ts">
import { elementLocale, loadMessages } from '@/locale/element-plus'
import { LOCALE_OPTIONS } from '@/locale/const'

import { shallowRef, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { setMetaTitle } from '@/utils/metaTitle'

const { locale } = useI18n()
const options = shallowRef(LOCALE_OPTIONS)

const route = useRoute()

watch(locale, () => {
  loadMessages(locale.value)
  setMetaTitle(<string>route.meta.title)
})
</script>
