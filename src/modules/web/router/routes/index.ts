import LayoutWeb from '@web/layout/index.vue'
import HomeView from '@web/views/Home/index.vue'
export default {
  path: '/',
  redirect: {
    name: 'home',
  },
  component: LayoutWeb,
  children: [
    {
      path: '',
      name: 'home',
      meta: { title: 'nav.home' },
      component: HomeView,
    },
    {
      path: 'refer-earn',
      name: 'refer-earn',
      meta: { title: 'refer.title' },
      component: () => import('@web/views/ReferEarn/index.vue'),
    },
    {
      path: 'claim',
      name: 'claim',
      component: () => import('@web/views/ReferEarn/Claim.vue'),
      meta: { title: 'claim.title' },
    },
    {
      path: 'shop-plans',
      name: 'shop-plans',
      meta: { title: 'nav.shop_plans' },
      component: () => import('@web/views/ShopPlans/index.vue'),
    },
    {
      path: 'plan/:pid',
      name: 'plan-detail',
      meta: { title: 'nav.shop_plans' },
      component: () => import('@web/views/ShopPlans/PlanDetail.vue'),
    },
    {
      path: 'checkout/:id',
      name: 'checkout',
      component: () => import('@web/views/Checkout/index.vue'),
      meta: { title: 'checkout.title' },
    },
    {
      path: 'checkout/promo',
      name: 'checkout-promo',
      component: () => import('@web/views/Promo/index.vue'),
      meta: { title: 'promo.title' },
    },
    {
      path: 'payment',
      name: 'payment',
      component: () => import('@web/views/Payment/index.vue'),
      meta: { title: 'payment.title' },
    },
    {
      path: 'my-esims',
      name: 'my-esims',
      meta: { title: 'nav.my_esims' },
      component: () => import('@web/views/MyEsims/index.vue'),
    },
    {
      path: 'esim-detail/:id',
      name: 'esim-detail',
      meta: { title: 'esimDetail.title' },
      component: () => import('@web/views/MyEsims/Detail.vue'),
    },
    {
      path: 'login',
      name: 'login',
      meta: { title: 'nav.login' },
      component: () => import('@web/views/Login/Login.vue'),
    },
    {
      path: 'register',
      name: 'register',
      meta: { title: 'Register' },
      component: () => import('@web/views/Register/Register.vue'),
    },
    {
      path: 'forgot-password',
      name: 'forgot-password',
      meta: { title: 'Forgot Password' },
      component: () => import('@web/views/ForgotPassword/ForgotPassword.vue'),
    },
    {
      path: 'country-selection',
      name: 'country-selection',
      meta: { title: 'Select Country' },
      component: () => import('@web/views/CountrySelection/CountrySelection.vue'),
    },
    {
      path: 'account-settings',
      name: 'account-settings',
      meta: { title: 'accountSettings.title' },
      component: () => import('@web/views/Setting/index.vue'),
    },
    {
      path: 'orders',
      name: 'orders',
      meta: { title: 'accountSettings.orders' },
      component: () => import('@web/views/Setting/OrdersPage.vue'),
    },
    {
      path: 'my-promos',
      name: 'my-promos',
      meta: { title: 'accountSettings.myPromos' },
      component: () => import('@web/views/Setting/MyPromosPage.vue'),
    },
    {
      path: 'points-rewards',
      name: 'points-rewards',
      meta: { title: 'accountSettings.pointsRewards' },
      component: () => import('@web/views/Setting/PointsRewardsPage.vue'),
    },
  ],
}
