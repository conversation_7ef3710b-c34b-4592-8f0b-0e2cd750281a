.footer {
  width: 100%;
  background-color: var(--vt-c-white-soft);
  padding: 160px 0 48px;
}

.footer-title {
  font-size: 64px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 48px;
  color: #000000;
}

.search-container {
  display: flex;
  justify-content: center;
  margin-bottom: 48px;
}

.footer-links-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 30px;
}

.footer-column {
  flex: 1;
  min-width: 200px;
}

.column-title {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 40px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    margin-bottom: 28px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  a {
    color: rgba(0, 0, 0, 0.5);
    text-decoration: none;
    font-size: 20px;
    transition: color 0.2s ease;

    &:hover {
      color: var(--base-color);
    }
  }
}

.social-icons {
  display: flex;
  flex-wrap: wrap;
  gap: 26px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.qr-code {
  margin-top: -25px;
  width: 144px;
  height: 144px;
  background-color: #ffffff;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
