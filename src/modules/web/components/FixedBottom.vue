<script lang="ts" setup>
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  text: {
    type: String,
    required: true,
  },
})

const EVENT_CLICK = 'click-button'
const emits = defineEmits([EVENT_CLICK])
const handleClick = function () {
  if (props.disabled) return
  emits(EVENT_CLICK)
}
</script>

<template>
  <div class="fixed-bottom">
    <div class="container-box">
      <div class="fixed-bottom__inner">
        <div class="fixed-content">
          <slot></slot>
        </div>
        <button
          :class="['operate-button', disabled && 'disabled']"
          :disabled="disabled"
          @click="handleClick"
        >
          {{ text }}
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use 'sass:color';

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: inset 0px 1px 0px 0px #e0e0e0;
  padding: 20px 0;
  z-index: 100;

  &__inner {
    display: flex;
    align-items: center;
  }

  .fixed-content {
    flex: 1;
  }

  .operate-button {
    flex: none;
    border: none;
    background-color: #222222;
    color: #fff;
    border-radius: 100px;
    min-width: 360px;
    padding: 24px 30px;
    font-size: 32px;
    line-height: 1;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &.disabled {
      cursor: not-allowed;
      background-color: #cccccc;
    }

    &:not(&.disabled):hover {
      background-color: color.adjust(#222222, $lightness: 5%);
    }
  }
}
</style>
