<script setup lang="ts">
import EsimSearch from '@/components/EsimSearch/index.vue'
import { Icon } from '@/components/Icon'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
import { LINKS_SOCIAL as socialLinks, LINKS_FOOTER as footerLinks } from '@/const/links'
</script>

<template>
  <section class="footer">
    <div class="container-box">
      <!-- 主标题 -->
      <h2 class="footer-title">{{ t('footer.start_travel') }}</h2>

      <!-- 搜索框 -->
      <div class="search-container">
        <EsimSearch size="large" align="center" />
      </div>

      <!-- 页脚链接区域 -->
      <div class="footer-links-container">
        <!-- 保持联系区域 -->
        <div class="footer-column">
          <h3 class="column-title">{{ t('footer.stay_connected') }}</h3>
          <ul class="footer-links">
            <li v-for="link in footerLinks.stayConnected" :key="link.name">
              <a :href="link.url">{{ t(link.name) }}</a>
            </li>
          </ul>
        </div>

        <!-- 关于我们区域 -->
        <div class="footer-column">
          <h3 class="column-title">{{ t('footer.about_us') }}</h3>
          <ul class="footer-links">
            <li v-for="link in footerLinks.aboutUs" :key="link.name">
              <a :href="link.url">{{ t(link.name) }}</a>
            </li>
          </ul>
        </div>

        <!-- 社交媒体区域 -->
        <div class="footer-column">
          <h3 class="column-title">{{ t('footer.follow_us') }}</h3>
          <div class="social-icons">
            <a
              v-for="social in socialLinks"
              :key="social.name"
              :href="social.url"
              :title="social.name"
              class="social-icon"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Icon :icon="`svg-icon:${social.icon}`" size="36"></Icon>
            </a>
          </div>
        </div>

        <!-- 二维码区域 -->
        <div class="footer-column">
          <h3 class="column-title">{{ t('footer.scan_download_app') }}</h3>
          <div class="qr-code">
            <img src="../images/download_qr.svg" alt="Download GlocalMe App" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style src="./Footer.scss" lang="scss" scoped></style>
