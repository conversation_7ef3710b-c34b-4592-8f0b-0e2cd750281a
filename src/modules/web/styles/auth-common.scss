// Web端认证页面公共样式

// 主容器样式
.auth-container {
  display: flex;
  justify-content: center;
  padding: 50px 20px;

  // 适配1920px宽度
  @media (min-width: 1920px) {
    max-width: 1920px;
    margin: 0 auto;
  }
}

// 表单卡片样式
.auth-form {
  width: 100%;
  max-width: 560px;
}

// 标题样式
.auth-title {
  font-size: 36px;
  font-weight: 600;
  color: #1f2937;
  text-align: left;
  margin-bottom: 58px;
  line-height: 1.3;
}

// 表单组样式
.form-group {
  margin-bottom: 40px;
}

// 表单标签样式
.form-label {
  display: block;
  font-size: 20px;
  font-weight: 500;
  color: #000000;
  margin-bottom: 12px;
}

// 输入框样式
:deep(.auth-input) {
  .el-input__wrapper {
    border-radius: 12px;
    border: 1px solid #e0e0e0;
  }

  .el-input__inner {
    font-size: 20px;

    &::placeholder {
      color: #9ca3af;
    }
  }
}

// 密码输入框包装器
.password-input-wrapper {
  position: relative;

  .password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #9ca3af;
    z-index: 10;
    font-size: 18px;
    &:hover {
      color: var(--base-color);
    }
  }
}

// 密码输入框特殊样式
:deep(.password-input) {
  .el-input__wrapper {
    padding-right: 40px;
  }
}

// 输入框包装器（带编辑图标）
.input-wrapper {
  position: relative;

  .edit-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #9ca3af;

    &:hover {
      color: var(--base-color);
    }
  }
}

// 密码头部（包含忘记密码链接）
.password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .forgot-password {
    color: var(--base-color);
    text-decoration: none;
    font-size: 14px;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 错误信息样式
.error-message {
  color: #ef4444;
  font-size: 16px;
  margin-top: 8px;
  line-height: 24px;
}

// 密码要求提示
.password-requirements {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 16px;
  text-align: left;
}

// 主要按钮样式
.auth-primary-btn {
  width: 100%;
  transition: all 0.2s ease;
  margin-bottom: 16px;
}

// 协议文本样式
.auth-agreement-text {
  font-size: 16px;
  color: #6b7280;
  text-align: left;
  line-height: 1.5;
  margin-bottom: 32px;

  .link {
    color: var(--base-color);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 分割线样式
.auth-divider {
  position: relative;
  text-align: center;
  margin: 60px 0;
  border-bottom: 1px solid #e5e7eb;
  .divider-text {
    background: #f6f6f6;
    color: #6b7280;
    padding: 0 24px;
    font-size: 16px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 500;
    top: -11px;
  }
}

// 社交登录样式
.auth-social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}
.auth-social-btn {
  width: 100%;
  margin-bottom: 20px;
  margin-left: 0;
  background: white;
  .el-icon {
    margin-right: 12px;
  }
  .social-icon {
    display: flex;
    align-items: center;
  }
  &.mobile-btn {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }
  }
}

// 链接样式
.auth-link {
  text-align: center;
  font-size: 14px;
  color: #6b7280;
  margin-top: 24px;

  .link {
    color: var(--base-color);
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 验证码输入样式
.verification-wrapper {
  display: flex;
  gap: 16px;
  align-items: center;

  .verification-input {
    flex: 1;
  }

  .send-code-btn {
    border-radius: 8px;
    width: 180px;
    font-size: 20px;
    font-weight: 500;
    min-width: 100px;
    color: #cccccc !important;
    border: 1px solid #e0e0e0;
    background: white;
    &:disabled {
      background: white !important;
      border: 1px solid #e0e0e0 !important;
    }
    &:not(.disabled) {
      background: white !important;
      border-color: var(--base-color) !important;
      color: var(--base-color) !important;

      &:hover {
        background: #047857;
        border-color: #047857;
      }
    }
  }
}

// 验证码输入框特殊样式
:deep(.verification-input) {
  .el-input__wrapper {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 12px 16px;
  }

  .el-input__inner {
    font-size: 20px;
    text-align: left;

    &::placeholder {
      color: #9ca3af;
    }
  }
}

// 次要按钮样式（如保存按钮）
.auth-secondary-btn {
  width: 100%;
  border-radius: 36px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}
// 登录页面特殊样式
.login-form {
  max-width: 560px;
}

// 注册页面特殊样式
.register-form {
  max-width: 560px;
}

// 地区选择页面特殊样式
.country-selection-form {
  max-width: 600px;
}
