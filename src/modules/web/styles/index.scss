// Web端样式入口文件
@use './variable.scss';
@use './container.scss';

/* 自定义样式优先级最高，覆盖Element Plus默认样式 */
.el-input__wrapper {
  height: var(--el-button-size) !important;
  box-shadow: none;
  color: #000000;
  background: white;
  border-radius: 12px;
  font-size: 20px;
  border: 1px solid #e0e0e0;
  &.is-focus {
    box-shadow: var(--base-shadow);
    border-color: var(--base-color);
  }
}
.el-form-item__error {
  font-size: 16px;
  margin-top: 4px;
  color: #ff2121;
}
input::input-placeholder {
  color: #cccccc;
  font-size: 20px;
}
input {
  color: var(--primary-color) !important;
}
.el-button {
  height: 72px;
  border-radius: 36px;
  font-size: 24px;
  color: var(--el-text-color-primary);
  &:disabled {
    &:hover {
      background: var(--el-disabled-bg-color) !important;
      border-color: var(--el-disabled-bg-color) !important;
    }
  }
  &:hover {
    border-color: 1px solid var(--base-color);
  }
  &.el-button--primary {
    color: white;
    &:hover {
      background: var(--el-button-hover-bg-color);
    }
  }
  &.el-button--default {
    background: white;
  }
  .el-icon {
    margin-right: 14px;
    font-size: 24px;
  }
  &.btn-secondary {
    background: #ffffff;
    border: 1px solid #999999;
    color: #000000;
    &:hover {
      background: #ffffff;
      color: var(--base-color);
    }
  }
}
.el-popper {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
  .el-select-dropdown__wrap {
    padding: 0 20px !important;
  }
  .el-select-dropdown__item {
    box-shadow: inset 0px -1px 0px 0px #e0e0e0;
    height: 56px;
    font-size: 20px;
    line-height: 56px;
    &:hover {
      background: rgba(0, 198, 94, 0.06);
    }
  }
}
.el-dialog {
  padding: 0 40px;
}
