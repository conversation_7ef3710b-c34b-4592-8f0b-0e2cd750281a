// 目的地类别
export enum DestinationType {
  LOCAL = 'local',
  REGION = 'region',
}

export const destinationTypes = [
  {
    label: 'home.popular.local',
    value: DestinationType.LOCAL,
  },
  {
    label: 'home.popular.regional',
    value: DestinationType.REGION,
  },
]

// 目的地数据
export const destinations = [
  { id: 1, name: 'United States', image: '/src/assets/images/destinations/united-states.jpg' },
  { id: 2, name: 'China', image: '/src/assets/images/destinations/china.jpg' },
  { id: 3, name: 'France', image: '/src/assets/images/destinations/france.jpg' },
  { id: 4, name: 'Singapore', image: '/src/assets/images/destinations/singapore.jpg' },
  { id: 5, name: 'Japan', image: '/src/assets/images/destinations/japan.jpg' },
  { id: 6, name: 'United Kingdom', image: '/src/assets/images/destinations/united-kingdom.jpg' },
  { id: 7, name: 'Germany', image: '/src/assets/images/destinations/germany.jpg' },
  { id: 8, name: 'Russia', image: '/src/assets/images/destinations/russia.jpg' },
  { id: 9, name: 'Canada', image: '/src/assets/images/destinations/canada.jpg' },
  { id: 10, name: 'Hongkong', image: '/src/assets/images/destinations/hongkong.jpg' },
  { id: 11, name: 'South Korea', image: '/src/assets/images/destinations/south-korea.jpg' },
  { id: 12, name: 'Spain', image: '/src/assets/images/destinations/spain.jpg' },
  { id: 13, name: 'Malaysia', image: '/src/assets/images/destinations/malaysia.jpg' },
  { id: 14, name: 'Australia', image: '/src/assets/images/destinations/australia.jpg' },
  { id: 15, name: 'New Zealand', image: '/src/assets/images/destinations/new-zealand.jpg' },
  { id: 16, name: 'Thailand', image: '/src/assets/images/destinations/thailand.jpg' },
]

// 分类编号
export const stages = [
  {
    label: 'ALL',
    value: '',
  },
  {
    label: 'A~G',
    value: 'ABCDEFG',
  },
  {
    label: 'H~N',
    value: 'HIJKLMN',
  },
  {
    label: 'O~T',
    value: 'OPQRST',
  },
  {
    label: 'U~Z',
    value: 'UVWXYZ',
  },
]
