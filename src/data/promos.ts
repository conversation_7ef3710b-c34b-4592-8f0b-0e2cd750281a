export interface PromoCode {
  id: number
  type: 'percent' | 'amount'
  value: number
  code: string
  validUntil: string
  isBestSaving: boolean
  isSelected: boolean
  isDisabled?: boolean
  condition?: string
}

export const promos: PromoCode[] = [
  {
    id: 1,
    type: 'percent',
    value: 50,
    code: 'GCU50',
    validUntil: '04/02/2025',
    isBestSaving: true,
    isSelected: true,
    condition: 'No-Minimum',
  },
  {
    id: 2,
    type: 'amount',
    value: 25,
    code: 'USD 25',
    validUntil: '04/02/2025',
    isBestSaving: false,
    isSelected: false,
    condition: 'No-Minimum',
  },
  {
    id: 3,
    type: 'percent',
    value: 60,
    code: 'GCU60',
    validUntil: '04/02/2025',
    isBestSaving: false,
    isSelected: false,
    condition: 'No-Minimum',
  },
  {
    id: 4,
    type: 'percent',
    value: 50,
    code: 'GCU50',
    validUntil: '04/02/2025',
    isBestSaving: false,
    isSelected: false,
    isDisabled: true,
    condition: 'Minimum spending of USD 100',
  },
]
