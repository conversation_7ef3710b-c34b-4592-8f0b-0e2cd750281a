// 国家/地区数据
export interface Country {
  code: string
  name: string
  nameEn: string
  flag?: string
}

export const countries: Country[] = [
  {
    code: 'US',
    name: '美国',
    nameEn: 'United States',
    flag: '🇺🇸',
  },
  {
    code: 'CN',
    name: '中国',
    nameEn: 'China',
    flag: '🇨🇳',
  },
  {
    code: 'GB',
    name: '英国',
    nameEn: 'United Kingdom',
    flag: '🇬🇧',
  },
  {
    code: 'CA',
    name: '加拿大',
    nameEn: 'Canada',
    flag: '🇨🇦',
  },
  {
    code: 'AU',
    name: '澳大利亚',
    nameEn: 'Australia',
    flag: '🇦🇺',
  },
  {
    code: 'DE',
    name: '德国',
    nameEn: 'Germany',
    flag: '🇩🇪',
  },
  {
    code: 'FR',
    name: '法国',
    nameEn: 'France',
    flag: '🇫🇷',
  },
  {
    code: 'JP',
    name: '日本',
    nameEn: 'Japan',
    flag: '🇯🇵',
  },
  {
    code: 'KR',
    name: '韩国',
    nameEn: 'South Korea',
    flag: '🇰🇷',
  },
  {
    code: 'SG',
    name: '新加坡',
    nameEn: 'Singapore',
    flag: '🇸🇬',
  },
  {
    code: 'HK',
    name: '香港',
    nameEn: 'Hong Kong',
    flag: '🇭🇰',
  },
  {
    code: 'TW',
    name: '台湾',
    nameEn: 'Taiwan',
    flag: '🇹🇼',
  },
  {
    code: 'IT',
    name: '意大利',
    nameEn: 'Italy',
    flag: '🇮🇹',
  },
  {
    code: 'ES',
    name: '西班牙',
    nameEn: 'Spain',
    flag: '🇪🇸',
  },
  {
    code: 'NL',
    name: '荷兰',
    nameEn: 'Netherlands',
    flag: '🇳🇱',
  },
  {
    code: 'CH',
    name: '瑞士',
    nameEn: 'Switzerland',
    flag: '🇨🇭',
  },
  {
    code: 'SE',
    name: '瑞典',
    nameEn: 'Sweden',
    flag: '🇸🇪',
  },
  {
    code: 'NO',
    name: '挪威',
    nameEn: 'Norway',
    flag: '🇳🇴',
  },
  {
    code: 'DK',
    name: '丹麦',
    nameEn: 'Denmark',
    flag: '🇩🇰',
  },
  {
    code: 'FI',
    name: '芬兰',
    nameEn: 'Finland',
    flag: '🇫🇮',
  },
  {
    code: 'BR',
    name: '巴西',
    nameEn: 'Brazil',
    flag: '🇧🇷',
  },
  {
    code: 'MX',
    name: '墨西哥',
    nameEn: 'Mexico',
    flag: '🇲🇽',
  },
  {
    code: 'IN',
    name: '印度',
    nameEn: 'India',
    flag: '🇮🇳',
  },
  {
    code: 'TH',
    name: '泰国',
    nameEn: 'Thailand',
    flag: '🇹🇭',
  },
  {
    code: 'MY',
    name: '马来西亚',
    nameEn: 'Malaysia',
    flag: '🇲🇾',
  },
  {
    code: 'ID',
    name: '印度尼西亚',
    nameEn: 'Indonesia',
    flag: '🇮🇩',
  },
  {
    code: 'PH',
    name: '菲律宾',
    nameEn: 'Philippines',
    flag: '🇵🇭',
  },
  {
    code: 'VN',
    name: '越南',
    nameEn: 'Vietnam',
    flag: '🇻🇳',
  },
  {
    code: 'AE',
    name: '阿联酋',
    nameEn: 'United Arab Emirates',
    flag: '🇦🇪',
  },
  {
    code: 'SA',
    name: '沙特阿拉伯',
    nameEn: 'Saudi Arabia',
    flag: '🇸🇦',
  },
  {
    code: 'IL',
    name: '以色列',
    nameEn: 'Israel',
    flag: '🇮🇱',
  },
  {
    code: 'TR',
    name: '土耳其',
    nameEn: 'Turkey',
    flag: '🇹🇷',
  },
  {
    code: 'RU',
    name: '俄罗斯',
    nameEn: 'Russia',
    flag: '🇷🇺',
  },
  {
    code: 'ZA',
    name: '南非',
    nameEn: 'South Africa',
    flag: '🇿🇦',
  },
  {
    code: 'EG',
    name: '埃及',
    nameEn: 'Egypt',
    flag: '🇪🇬',
  },
  {
    code: 'NG',
    name: '尼日利亚',
    nameEn: 'Nigeria',
    flag: '🇳🇬',
  },
  {
    code: 'KE',
    name: '肯尼亚',
    nameEn: 'Kenya',
    flag: '🇰🇪',
  },
  {
    code: 'AR',
    name: '阿根廷',
    nameEn: 'Argentina',
    flag: '🇦🇷',
  },
  {
    code: 'CL',
    name: '智利',
    nameEn: 'Chile',
    flag: '🇨🇱',
  },
  {
    code: 'CO',
    name: '哥伦比亚',
    nameEn: 'Colombia',
    flag: '🇨🇴',
  },
  {
    code: 'PE',
    name: '秘鲁',
    nameEn: 'Peru',
    flag: '🇵🇪',
  },
  {
    code: 'NZ',
    name: '新西兰',
    nameEn: 'New Zealand',
    flag: '🇳🇿',
  },
]

// 根据语言获取国家名称
export const getCountryName = (country: Country, locale: string): string => {
  return locale === 'zh-cn' ? country.name : country.nameEn
}

// 根据代码查找国家
export const findCountryByCode = (code: string): Country | undefined => {
  return countries.find((country) => country.code === code)
}
